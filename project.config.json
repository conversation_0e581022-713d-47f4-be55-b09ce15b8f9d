{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "packOptions": {"ignore": [], "include": []}, "setting": {"bundle": false, "userConfirmedBundleSwitch": false, "urlCheck": true, "scopeDataCheck": false, "coverView": true, "es6": true, "postcss": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "minified": true, "autoAudits": false, "newFeature": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useIsolateContext": true, "nodeModules": false, "enhance": true, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "enableEngineNative": false, "packNpmRelationList": [], "minifyWXSS": true, "showES6CompileOption": false, "minifyWXML": true, "useStaticServer": true, "checkInvalidKey": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "disableUseStrict": false, "useCompilerPlugins": false}, "compileType": "miniprogram", "libVersion": "2.19.4", "appid": "wx8a465c944dfbd26f", "projectname": "miniprogram-92", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}