<wxs module="utils">
  module.exports.max = function (n1, n2) {
    return Math.max(n1, n2)
  }
  module.exports.len = function (arr) {
    arr = arr || []
    return arr.length
  }
</wxs>
<view>
  <button bindtap="openBluetoothAdapter" style="width: 30%; display: inline-block; margin: 5px;font-size: 15px;">开始扫描</button>
  <button bindtap="stopBluetoothDevicesDiscovery" style="width: 30%; display: inline-block;margin: 5px;font-size: 15px;">停止扫描</button>
  <button bindtap="closeBluetoothAdapter" style="width: 30%; display: inline-block;margin: 5px;font-size: 15px;">结束流程</button>
<text>{{udpport}}</text>
</view>

<view class="devices_summary">
<view>
  已发现 {{devices.length}} 个外围设备：
 <text style="color: red;" wx:if="{{connected}}">已连接到 {{name}}</text>
  </view>

</view>
<scroll-view class="device_list" scroll-y scroll-with-animation>
  <view wx:for="{{devices}}" wx:key="index" data-device-id="{{item.deviceId}}" data-name="{{item.name || item.localName}}" bindtap="createBLEConnection" class="device_item" hover-class="device_item_hover">
    <view style="font-size: 16px; color: #333;">{{item.name}}</view>
    <view style="font-size: 10px">信号强度: {{item.RSSI}}dBm ({{utils.max(0, item.RSSI + 100)}}%)</view>
    <view style="font-size: 10px">UUID: {{item.deviceId}}</view>
    <view style="font-size: 10px">Service数量: {{utils.len(item.advertisServiceUUIDs)}}</view>
  </view>
</scroll-view>
<view>
  <view class="data_top">
    {{rPupil}}
  </view>
  <view class="data_top ">
    {{pd}}
  </view>
  <view class="data_top">
    {{lPupil}}
  </view>
  <view class="data_top data_top_title">
    mm
  </view>
  <view class="data_top data_top_title ">
    mm
  </view>
  <view class="data_top data_top_title">
    mm
  </view>

  <view class="data_top">
    {{rGazeV}}
  </view>
  <view class="data_top ">

  </view>
  <view class="data_top">
    {{lGazeV}}
  </view>
  <view class="data_top">
    -
  </view>
  <view class="data_top ">

  </view>
  <view class="data_top">
    -
  </view>
  <view class="data_top">
    {{rGazeH}}
  </view>
  <view class="data_top ">

  </view>
  <view class="data_top">
    {{lGazeH}}
  </view>
  <view >
    <button  bindtap="deviceCylChange">{{deviceCyl?"负散":"正散"}}</button>
  </view>
  <view wx:if="{{deviceCyl}}">
    <view class="data_Content">
      <view>
        {{rDS1}}
      </view>
      <view>
        {{rDC1}}
      </view>
      <view>
        {{rAxis1}}
      </view>
      <view>
        {{rSE1}}
      </view>
    </view>
    <view class="data_Content">
      <view>
        {{lDS1}}
      </view>
      <view>
        {{lDC1}}
      </view>
      <view>
        {{lAxis1}}
      </view>
      <view>
        {{lSE1}}
      </view>
    </view>
  </view>

  <view wx:if="{{!deviceCyl}}">
    <view class="data_Content">
      <view>
        {{rDS2}}
      </view>
      <view>
        {{rDC2}}
      </view>
      <view>
        {{rAxis2}}
      </view>
      <view>
        {{rSE2}}
      </view>
    </view>
    <view class="data_Content">
      <view>
        {{lDS2}}
      </view>
      <view>
        {{lDC2}}
      </view>
      <view>
        {{lAxis2}}
      </view>
      <view>
        {{lSE2}}
      </view>
    </view>
  </view>
  <view>
  </view>
</view>
<view class="connected_info"  wx:if="{{connected}}">
  <view>
    <textarea style="height:50px;background-color: white; width: 95%;" value="{{sendDataValue}}" bindinput="sendDataValueChange" ></textarea>
  </view>
    <view class="operation">
      <button style="margin-right: 3px;" size="mini"  disabled="{{response}}" bindtap="sendData" >发送数据</button>
      <button style="margin-right: 3px;" size="mini" bindtap="clearData" >清空数据</button>
      <button size="mini" bindtap="closeBLEConnection">断开连接</button>
    </view>
  <view>
  </view>
</view>