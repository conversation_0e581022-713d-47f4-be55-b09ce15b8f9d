page {
  color: #333;
}
.devices_summary {
  padding: 10px;
  font-size: 16px;
}
.device_list {
  height: 180px;
  margin: 50px 5px;
  margin-top: 0;
  border: 1px solid #EEE;
  border-radius: 5px;
  width: auto;
}
.device_item {
  border-bottom: 1px solid #EEE;
  padding: 10px;
  color: #666;
}
.device_item_hover {
  background-color: rgba(0, 0, 0, .1);
}
.connected_info {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: #F0F0F0;
  padding: 10px;
  padding-bottom: 20px;
  margin-bottom: env(safe-area-inset-bottom);
  font-size: 14px;
  height: 75px;
  box-shadow: 0px 0px 3px 0px;
}
.connected_info .operation {
  position: absolute;
  display: inline-block;
  margin-top: 10px;
}


.data_top{
  width: 33.3%;
  display: inline-block  !important; 
  text-align: center; 
 
}
.data_top_title{
  font-size: 12px;
}

.data_Content{
  width: 50%; display: inline-block !important;  text-align: center;
}

.data_Content view{
  width: 33.3%;
  display: inline-block  !important; 
  text-align: center; 
}