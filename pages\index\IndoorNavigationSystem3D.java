package com.java110.core.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.awt.geom.*;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import org.locationtech.proj4j.*;

/**
 * 导览屏3D两点规划路线服务 - 高性能优化版
 */
public class IndoorNavigationSystem3D {

    private static Building building;
    private static final PathfindingAlgorithm pathAlgorithm = new AStar3DAlgorithm();
    private static boolean isInitialized = false;
    private static final double FLOOR_HEIGHT = 4.0; // 楼层高度常量

    // 缓存已计算的网格规划器
    private static final Map<Building, GridPathPlanner> plannerCache = new ConcurrentHashMap<>();

    // 坐标转换器
    private static CoordinateTransformer coordinateTransformer;

    public static synchronized void initialize(String resourcePath) throws IOException {
        if (!isInitialized) {
            building = loadBuildingData(resourcePath);
            isInitialized = true;
        }
    }

    // 坐标转换工具类 - 修改为直接使用3857坐标
    public static class CoordinateTransformer {
        // 由于坐标已经是3857格式，不需要转换，直接返回原始坐标
        public static Point2D.Double transformToWebMercator(double x, double y) {
            // 直接返回原始坐标（已经是3857格式）
            return new Point2D.Double(x, y);
        }

        public static Point2D.Double transformToWGS84(double x, double y) {
            // 如果需要反向转换，可以在这里实现
            // 但目前直接返回原始坐标
            return new Point2D.Double(x, y);
        }
    }

    // 高性能网格路径规划器
    public static class GridPathPlanner {
        private double gridSize; // 网格大小（米）
        private Map<Integer, GridFloor> floorGrids = new HashMap<>();
        private Building building;

        public GridPathPlanner(Building building) {
            this.building = building;
            this.gridSize = calculateOptimalGridSize(building);
            initializeGrids();
        }

        // 根据通道面积和障碍物密度计算最优网格大小
        private double calculateOptimalGridSize(Building building) {
            double totalArea = 0;
            int totalObstacles = 0;
            int totalShops = 0;

            for (Floor floor : building.getFloors()) {
                // 计算通道面积
                for (Node passage : building.getNodesOnFloor(floor.getNumber())) {
                    if (passage.getType() == NodeType.PASSAGE && passage.getPolygon() != null) {
                        totalArea += calculatePolygonArea(passage.getPolygon());
                    }
                }

                // 统计障碍物和商铺数量
                totalObstacles += floor.getObstacles().size();
                totalShops += building.getShops().stream()
                        .mapToInt(shop -> shop.getFloor() == floor.getNumber() ? 1 : 0)
                        .sum();
            }

            // 计算障碍物密度
            double obstacleDensity = (totalObstacles + totalShops) / Math.max(totalArea, 1.0);

            // 自适应网格大小计算
            double baseGridSize;
            if (totalArea > 10000) {
                baseGridSize = 2.0; // 大型区域基础网格
            } else if (totalArea > 5000) {
                baseGridSize = 1.5; // 中型区域基础网格
            } else if (totalArea > 1000) {
                baseGridSize = 1.0; // 小型区域基础网格
            } else {
                baseGridSize = 0.5; // 很小区域基础网格
            }

            // 根据障碍物密度调整网格大小
            if (obstacleDensity > 0.01) { // 高密度障碍物
                baseGridSize *= 0.5;
            } else if (obstacleDensity > 0.005) { // 中密度障碍物
                baseGridSize *= 0.7;
            } else if (obstacleDensity > 0.001) { // 低密度障碍物
                baseGridSize *= 0.85;
            }

            // 限制网格大小范围
            return Math.max(0.2, Math.min(2.0, baseGridSize));
        }

        // 计算多边形面积（鞋带公式）
        private double calculatePolygonArea(List<Point2D> polygon) {
            double area = 0;
            for (int i = 0, j = polygon.size() - 1; i < polygon.size(); j = i++) {
                Point2D p1 = polygon.get(j);
                Point2D p2 = polygon.get(i);
                area += (p1.getX() * p2.getY()) - (p2.getX() * p1.getY());
            }
            return Math.abs(area / 2);
        }

        // 初始化所有楼层的网格
        private void initializeGrids() {
            for (Floor floor : building.getFloors()) {
                // 找到该楼层上的通道节点
                List<Node> passageNodes = building.getNodesOnFloor(floor.getNumber()).stream()
                        .filter(n -> n.getType() == NodeType.PASSAGE)
                        .collect(Collectors.toList());

                if (!passageNodes.isEmpty()) {
                    // 假设每层只有一个通道节点
                    Node passage = passageNodes.get(0);
                    if (passage.getPolygon() != null) {
                        floorGrids.put(floor.getNumber(), new GridFloor(floor.getNumber(), passage.getPolygon()));
                    }
                }
            }
        }

        // 在网格上查找路径（异步版本）
        public CompletableFuture<List<Point2D>> findPathOnGridAsync(int floorNum, double startX, double startY,
                double endX, double endY) {
            return CompletableFuture.supplyAsync(() -> findPathOnGrid(floorNum, startX, startY, endX, endY));
        }

        // 在网格上查找路径
        public List<Point2D> findPathOnGrid(int floorNum, double startX, double startY, double endX, double endY) {
            GridFloor gridFloor = floorGrids.get(floorNum);
            if (gridFloor == null) {
                System.out.println("楼层 " + floorNum + " 没有网格数据");
                return null;
            }

            // 将实际坐标转换为网格坐标
            GridPoint startGrid = gridFloor.toGridPoint(startX, startY);
            GridPoint endGrid = gridFloor.toGridPoint(endX, endY);

            System.out.println(
                    "网格路径规划: 起点(" + startGrid.x + "," + startGrid.y + ") -> 终点(" + endGrid.x + "," + endGrid.y + ")");

            // 首先尝试使用跳点搜索A*算法
            List<GridPoint> gridPath = aStarWithJumpPointSearch(gridFloor, startGrid, endGrid);

            // 如果跳点搜索失败，回退到标准A*算法
            if (gridPath == null || gridPath.isEmpty()) {
                System.out.println("跳点搜索失败，回退到标准A*算法");
                gridPath = aStarOnGrid(gridFloor, startGrid, endGrid);

                if (gridPath == null || gridPath.isEmpty()) {
                    System.out.println("网格A*算法未找到路径");
                    return null;
                }
            }

            System.out.println("找到网格路径，点数: " + gridPath.size());

            // 将网格路径转换为实际坐标路径
            List<Point2D> path = gridPath.stream()
                    .map(p -> gridFloor.toActualPoint(p))
                    .collect(Collectors.toList());

            // 路径平滑处理
            return smoothPath(path, gridFloor);
        }

        // 完整的网格A*算法实现
        private List<GridPoint> aStarOnGrid(GridFloor gridFloor, GridPoint start, GridPoint end) {
            System.out.println("A* started from " + start.x + "," + start.y + " to " + end.x + "," + end.y);

            // 开放列表（待评估节点）
            PriorityQueue<GridNode> openSet = new PriorityQueue<>();
            // 关闭列表（已评估节点）
            Set<GridPoint> closedSet = new HashSet<>();
            // 记录每个节点的来源
            Map<GridPoint, GridPoint> cameFrom = new HashMap<>();
            // 从起点到当前节点的实际代价
            Map<GridPoint, Double> gScore = new HashMap<>();
            // 从起点经过当前节点到终点的估计代价
            Map<GridPoint, Double> fScore = new HashMap<>();

            // 初始化起点
            gScore.put(start, 0.0);
            fScore.put(start, heuristic(start, end));
            openSet.add(new GridNode(start, fScore.get(start)));
            System.out.println("Added start to openSet: " + start.x + "," + start.y);

            while (!openSet.isEmpty()) {
                // 获取f值最小的节点
                GridNode currentNode = openSet.poll();
                GridPoint current = currentNode.point;
                System.out.println(
                        "Processing: " + current.x + "," + current.y + " (fScore: " + currentNode.fScore + ")");

                // 如果到达终点，重建路径
                if (current.equals(end)) {
                    System.out.println("Reached destination");
                    return reconstructPath(cameFrom, current);
                }

                closedSet.add(current);
                System.out.println("Added to closedSet: " + current.x + "," + current.y);

                // 检查所有相邻节点
                List<GridPoint> neighbors = getNeighbors(gridFloor, current);
                System.out.println("Found " + neighbors.size() + " neighbors");

                for (GridPoint neighbor : neighbors) {
                    System.out.println("Checking neighbor: " + neighbor.x + "," + neighbor.y);

                    if (closedSet.contains(neighbor)) {
                        System.out.println("Neighbor in closedSet, skipping");
                        continue;
                    }

                    // 计算从起点到邻居节点的代价
                    double tentativeGScore = gScore.get(current) + distance(current, neighbor);
                    System.out.println("Tentative G score: " + tentativeGScore);

                    // 如果这不是一条更好的路径，跳过
                    if (tentativeGScore >= gScore.getOrDefault(neighbor, Double.MAX_VALUE)) {
                        System.out.println("Not a better path, skipping");
                        continue;
                    }

                    // 这条路径更好，记录它
                    cameFrom.put(neighbor, current);
                    gScore.put(neighbor, tentativeGScore);
                    double h = heuristic(neighbor, end);
                    fScore.put(neighbor, tentativeGScore + h);
                    System.out.println(
                            "Updated scores: g=" + tentativeGScore + ", h=" + h + ", f=" + fScore.get(neighbor));

                    // 如果邻居不在开放列表中，添加它
                    if (!openSetContains(openSet, neighbor)) {
                        openSet.add(new GridNode(neighbor, fScore.get(neighbor)));
                        System.out.println("Added to openSet: " + neighbor.x + "," + neighbor.y);
                    } else {
                        System.out.println("Already in openSet: " + neighbor.x + "," + neighbor.y);
                    }
                }

                System.out.println("OpenSet size: " + openSet.size());
            }

            // 开放列表为空，没有找到路径
            System.out.println("No path found");
            return null;
        }

        // 获取相邻网格点
        private List<GridPoint> getNeighbors(GridFloor gridFloor, GridPoint point) {
            List<GridPoint> neighbors = new ArrayList<>();
            int[][] directions = {
                    { 0, 1 }, { 1, 0 }, { 0, -1 }, { -1, 0 }, // 上下左右
                    { 1, 1 }, { 1, -1 }, { -1, 1 }, { -1, -1 } // 对角线
            };

            for (int[] dir : directions) {
                int newX = point.x + dir[0];
                int newY = point.y + dir[1];

                // 检查边界
                if (newX >= 0 && newX < gridFloor.gridWidth &&
                        newY >= 0 && newY < gridFloor.gridHeight) {
                    GridPoint neighbor = new GridPoint(newX, newY);

                    // 检查是否可行走
                    if (gridFloor.walkable[newX][newY]) {
                        neighbors.add(neighbor);
                    }
                }
            }

            return neighbors;
        }

        // 计算两点间的欧几里得距离
        private double distance(GridPoint a, GridPoint b) {
            return Math.sqrt(Math.pow(a.x - b.x, 2) + Math.pow(a.y - b.y, 2));
        }

        // 改进的启发式函数（考虑障碍物密度和方向性）
        private double heuristic(GridPoint a, GridPoint b) {
            // 基础欧几里得距离
            double euclideanDistance = Math.sqrt(Math.pow(a.x - b.x, 2) + Math.pow(a.y - b.y, 2));

            // 添加方向性权重（鼓励朝目标方向移动）
            double dx = b.x - a.x;
            double dy = b.y - a.y;
            double cross = Math.abs(dx * (b.y - a.y) - dy * (b.x - a.x));
            double directionalPenalty = cross * 0.001; // 轻微惩罚偏离直线的路径

            return euclideanDistance + directionalPenalty;
        }

        // 改进的A*算法，支持跳点搜索优化
        private List<GridPoint> aStarWithJumpPointSearch(GridFloor gridFloor, GridPoint start, GridPoint end) {
            System.out.println("使用跳点搜索A*算法: " + start.x + "," + start.y + " to " + end.x + "," + end.y);

            PriorityQueue<GridNode> openSet = new PriorityQueue<>();
            Set<GridPoint> closedSet = new HashSet<>();
            Map<GridPoint, GridPoint> cameFrom = new HashMap<>();
            Map<GridPoint, Double> gScore = new HashMap<>();
            Map<GridPoint, Double> fScore = new HashMap<>();

            gScore.put(start, 0.0);
            fScore.put(start, heuristic(start, end));
            openSet.add(new GridNode(start, fScore.get(start)));

            while (!openSet.isEmpty()) {
                GridNode currentNode = openSet.poll();
                GridPoint current = currentNode.point;

                if (current.equals(end)) {
                    return reconstructPath(cameFrom, current);
                }

                closedSet.add(current);

                // 使用跳点搜索获取邻居
                List<GridPoint> neighbors = getJumpPointNeighbors(gridFloor, current, end);

                for (GridPoint neighbor : neighbors) {
                    if (closedSet.contains(neighbor)) {
                        continue;
                    }

                    double tentativeGScore = gScore.get(current) + distance(current, neighbor);

                    if (tentativeGScore < gScore.getOrDefault(neighbor, Double.MAX_VALUE)) {
                        cameFrom.put(neighbor, current);
                        gScore.put(neighbor, tentativeGScore);
                        double h = heuristic(neighbor, end);
                        fScore.put(neighbor, tentativeGScore + h);

                        if (!openSetContains(openSet, neighbor)) {
                            openSet.add(new GridNode(neighbor, fScore.get(neighbor)));
                        }
                    }
                }
            }

            return null; // 未找到路径
        }

        // 跳点搜索邻居获取
        private List<GridPoint> getJumpPointNeighbors(GridFloor gridFloor, GridPoint current, GridPoint goal) {
            List<GridPoint> neighbors = new ArrayList<>();

            // 8个方向的搜索
            int[][] directions = {
                    { 0, 1 }, { 1, 0 }, { 0, -1 }, { -1, 0 }, // 直线方向
                    { 1, 1 }, { 1, -1 }, { -1, 1 }, { -1, -1 } // 对角线方向
            };

            for (int[] dir : directions) {
                GridPoint jumpPoint = jump(gridFloor, current, dir[0], dir[1], goal);
                if (jumpPoint != null) {
                    neighbors.add(jumpPoint);
                }
            }

            return neighbors;
        }

        // 跳点搜索核心方法
        private GridPoint jump(GridFloor gridFloor, GridPoint current, int dx, int dy, GridPoint goal) {
            int newX = current.x + dx;
            int newY = current.y + dy;

            // 检查边界和可行走性
            if (newX < 0 || newX >= gridFloor.gridWidth ||
                    newY < 0 || newY >= gridFloor.gridHeight ||
                    !gridFloor.walkable[newX][newY]) {
                return null;
            }

            GridPoint next = new GridPoint(newX, newY);

            // 如果到达目标
            if (next.equals(goal)) {
                return next;
            }

            // 检查强制邻居（forced neighbors）
            if (hasForcedNeighbors(gridFloor, next, dx, dy)) {
                return next;
            }

            // 对角线移动时，检查水平和垂直方向的跳点
            if (dx != 0 && dy != 0) {
                if (jump(gridFloor, next, dx, 0, goal) != null ||
                        jump(gridFloor, next, 0, dy, goal) != null) {
                    return next;
                }
            }

            // 继续在同一方向跳跃
            return jump(gridFloor, next, dx, dy, goal);
        }

        // 检查是否有强制邻居
        private boolean hasForcedNeighbors(GridFloor gridFloor, GridPoint point, int dx, int dy) {
            int x = point.x;
            int y = point.y;

            if (dx != 0 && dy != 0) { // 对角线移动
                // 检查对角线移动的强制邻居
                if ((!isWalkable(gridFloor, x - dx, y) && isWalkable(gridFloor, x - dx, y + dy)) ||
                        (!isWalkable(gridFloor, x, y - dy) && isWalkable(gridFloor, x + dx, y - dy))) {
                    return true;
                }
            } else if (dx != 0) { // 水平移动
                if ((!isWalkable(gridFloor, x, y + 1) && isWalkable(gridFloor, x + dx, y + 1)) ||
                        (!isWalkable(gridFloor, x, y - 1) && isWalkable(gridFloor, x + dx, y - 1))) {
                    return true;
                }
            } else if (dy != 0) { // 垂直移动
                if ((!isWalkable(gridFloor, x + 1, y) && isWalkable(gridFloor, x + 1, y + dy)) ||
                        (!isWalkable(gridFloor, x - 1, y) && isWalkable(gridFloor, x - 1, y + dy))) {
                    return true;
                }
            }

            return false;
        }

        // 辅助方法：检查网格点是否可行走
        private boolean isWalkable(GridFloor gridFloor, int x, int y) {
            if (x < 0 || x >= gridFloor.gridWidth || y < 0 || y >= gridFloor.gridHeight) {
                return false;
            }
            return gridFloor.walkable[x][y];
        }

        // 重建路径
        private List<GridPoint> reconstructPath(Map<GridPoint, GridPoint> cameFrom, GridPoint current) {
            List<GridPoint> path = new ArrayList<>();
            path.add(current);

            while (cameFrom.containsKey(current)) {
                current = cameFrom.get(current);
                path.add(0, current);
            }

            return path;
        }

        // 检查开放列表中是否包含某个点
        private boolean openSetContains(PriorityQueue<GridNode> openSet, GridPoint point) {
            for (GridNode node : openSet) {
                if (node.point.equals(point)) {
                    return true;
                }
            }
            return false;
        }

        // 高级路径平滑处理
        private List<Point2D> smoothPath(List<Point2D> path, GridFloor gridFloor) {
            if (path.size() < 3)
                return path;

            // 第一步：Douglas-Peucker简化
            List<Point2D> simplified = douglasPeuckerSimplify(path, 0.5); // 0.5米容差

            // 第二步：贝塞尔曲线平滑
            List<Point2D> smoothed = bezierSmooth(simplified, gridFloor);

            // 第三步：验证平滑后的路径
            if (validateSmoothedPath(smoothed, gridFloor)) {
                return smoothed;
            } else {
                // 如果验证失败，返回简化后的路径
                System.out.println("贝塞尔平滑验证失败，返回简化路径");
                return simplified;
            }
        }

        // Douglas-Peucker路径简化算法
        private List<Point2D> douglasPeuckerSimplify(List<Point2D> path, double tolerance) {
            if (path.size() < 3)
                return path;

            List<Point2D> simplified = new ArrayList<>();
            douglasPeuckerRecursive(path, 0, path.size() - 1, tolerance, simplified);

            // 确保起点和终点都包含在内
            if (!simplified.contains(path.get(0))) {
                simplified.add(0, path.get(0));
            }
            if (!simplified.contains(path.get(path.size() - 1))) {
                simplified.add(path.get(path.size() - 1));
            }

            // 按原始路径顺序排序
            simplified.sort((p1, p2) -> {
                int index1 = path.indexOf(p1);
                int index2 = path.indexOf(p2);
                return Integer.compare(index1, index2);
            });

            return simplified;
        }

        // Douglas-Peucker递归实现
        private void douglasPeuckerRecursive(List<Point2D> path, int start, int end,
                double tolerance, List<Point2D> result) {
            if (end - start < 2) {
                result.add(path.get(start));
                if (end != start)
                    result.add(path.get(end));
                return;
            }

            // 找到距离起点-终点连线最远的点
            double maxDistance = 0;
            int maxIndex = start;

            Point2D startPoint = path.get(start);
            Point2D endPoint = path.get(end);

            for (int i = start + 1; i < end; i++) {
                double distance = pointToLineDistance(path.get(i), startPoint, endPoint);
                if (distance > maxDistance) {
                    maxDistance = distance;
                    maxIndex = i;
                }
            }

            // 如果最大距离超过容差，递归处理
            if (maxDistance > tolerance) {
                douglasPeuckerRecursive(path, start, maxIndex, tolerance, result);
                douglasPeuckerRecursive(path, maxIndex, end, tolerance, result);
            } else {
                result.add(path.get(start));
                result.add(path.get(end));
            }
        }

        // 计算点到直线的距离
        private double pointToLineDistance(Point2D point, Point2D lineStart, Point2D lineEnd) {
            double A = lineEnd.getY() - lineStart.getY();
            double B = lineStart.getX() - lineEnd.getX();
            double C = lineEnd.getX() * lineStart.getY() - lineStart.getX() * lineEnd.getY();

            return Math.abs(A * point.getX() + B * point.getY() + C) /
                    Math.sqrt(A * A + B * B);
        }

        // 贝塞尔曲线平滑
        private List<Point2D> bezierSmooth(List<Point2D> path, GridFloor gridFloor) {
            if (path.size() < 3)
                return path;

            List<Point2D> smoothed = new ArrayList<>();
            smoothed.add(path.get(0)); // 添加起点

            for (int i = 1; i < path.size() - 1; i++) {
                Point2D prev = path.get(i - 1);
                Point2D current = path.get(i);
                Point2D next = path.get(i + 1);

                // 生成贝塞尔曲线段
                List<Point2D> curvePoints = generateBezierCurve(prev, current, next, 5);

                // 验证曲线点是否可行走
                boolean curveValid = true;
                for (Point2D curvePoint : curvePoints) {
                    if (!gridFloor.isPointWalkable(curvePoint)) {
                        curveValid = false;
                        break;
                    }
                }

                if (curveValid) {
                    smoothed.addAll(curvePoints);
                } else {
                    // 如果曲线不可行，使用直线
                    smoothed.add(current);
                }
            }

            smoothed.add(path.get(path.size() - 1)); // 添加终点
            return smoothed;
        }

        // 生成二次贝塞尔曲线
        private List<Point2D> generateBezierCurve(Point2D p0, Point2D p1, Point2D p2, int segments) {
            List<Point2D> curve = new ArrayList<>();

            for (int i = 0; i <= segments; i++) {
                double t = (double) i / segments;
                double x = (1 - t) * (1 - t) * p0.getX() +
                        2 * (1 - t) * t * p1.getX() +
                        t * t * p2.getX();
                double y = (1 - t) * (1 - t) * p0.getY() +
                        2 * (1 - t) * t * p1.getY() +
                        t * t * p2.getY();

                curve.add(new Point2D.Double(x, y));
            }

            return curve;
        }

        // 验证平滑后的路径
        private boolean validateSmoothedPath(List<Point2D> path, GridFloor gridFloor) {
            for (Point2D point : path) {
                if (!gridFloor.isPointWalkable(point)) {
                    return false;
                }
            }

            // 检查相邻点之间的连线是否可行走
            for (int i = 0; i < path.size() - 1; i++) {
                if (!isDirectPathWalkable(path.get(i), path.get(i + 1), gridFloor)) {
                    return false;
                }
            }

            return true;
        }

        // 检查两点间直线路径是否可行走
        private boolean isDirectPathWalkable(Point2D from, Point2D to, GridFloor gridFloor) {
            Line2D line = new Line2D.Double(from, to);

            // 检查线段上的多个点是否都可行走
            int steps = 10;
            for (int i = 1; i < steps; i++) {
                double ratio = i / (double) steps;
                double x = from.getX() + ratio * (to.getX() - from.getX());
                double y = from.getY() + ratio * (to.getY() - from.getY());

                if (!gridFloor.isPointWalkable(new Point2D.Double(x, y))) {
                    return false;
                }
            }

            return true;
        }

        // 网格楼层类
        class GridFloor {
            private final int floorNum;
            private final List<Point2D> polygon;
            private double minX, maxX, minY, maxY;
            private int gridWidth, gridHeight;
            private boolean[][] walkable;
            private final Map<String, List<Obstacle>> obstacleSpatialHash = new HashMap<>();
            private Quadtree walkableQuadtree;
            private RTree obstacleRTree; // R-tree索引障碍物
            private RTree shopRTree; // R-tree索引商铺

            public GridFloor(int floorNum, List<Point2D> polygon) {
                this.floorNum = floorNum;
                this.polygon = polygon;

                // 计算多边形边界
                calculateBounds();

                // 初始化网格
                initializeGrid();

                // 初始化空间哈希
                initializeSpatialHash();

                // 初始化四叉树
                initializeQuadtree();

                // 初始化R-tree索引
                initializeRTrees();

                System.out.println("初始化楼层 " + floorNum + " 网格: " + gridWidth + "x" + gridHeight +
                        ", 边界: " + minX + "," + minY + " -> " + maxX + "," + maxY);
            }

            private void calculateBounds() {
                minX = Double.MAX_VALUE;
                maxX = Double.MIN_VALUE;
                minY = Double.MAX_VALUE;
                maxY = Double.MIN_VALUE;

                for (Point2D p : polygon) {
                    minX = Math.min(minX, p.getX());
                    maxX = Math.max(maxX, p.getX());
                    minY = Math.min(minY, p.getY());
                    maxY = Math.max(maxY, p.getY());
                }
            }

            private void initializeGrid() {
                // 计算网格尺寸
                gridWidth = (int) Math.ceil((maxX - minX) / gridSize);
                gridHeight = (int) Math.ceil((maxY - minY) / gridSize);

                // 初始化可行走网格
                walkable = new boolean[gridWidth][gridHeight];

                int walkableCount = 0;

                // 标记可行走区域
                for (int x = 0; x < gridWidth; x++) {
                    for (int y = 0; y < gridHeight; y++) {
                        Point2D point = toActualPoint(new GridPoint(x, y));
                        walkable[x][y] = isPointWalkable(point);
                        if (walkable[x][y])
                            walkableCount++;
                    }
                }

                System.out.println("可行走网格点: " + walkableCount + "/" + (gridWidth * gridHeight));
            }

            private void initializeSpatialHash() {
                int cellSize = (int) Math.ceil(gridSize * 5); // 每5个网格一个单元格

                Floor floor = building.getFloor(floorNum);
                if (floor == null)
                    return;

                for (Obstacle obs : floor.getObstacles()) {
                    Rectangle2D bounds = getPolygonBounds(obs.getPolygon());
                    int minCellX = (int) (bounds.getMinX() / cellSize);
                    int maxCellX = (int) (bounds.getMaxX() / cellSize);
                    int minCellY = (int) (bounds.getMinY() / cellSize);
                    int maxCellY = (int) (bounds.getMaxY() / cellSize);

                    for (int x = minCellX; x <= maxCellX; x++) {
                        for (int y = minCellY; y <= maxCellY; y++) {
                            String key = x + "," + y;
                            obstacleSpatialHash.computeIfAbsent(key, k -> new ArrayList<>()).add(obs);
                        }
                    }
                }
            }

            private void initializeQuadtree() {
                walkableQuadtree = new Quadtree(minX, minY, maxX - minX, maxY - minY);

                for (int x = 0; x < gridWidth; x++) {
                    for (int y = 0; y < gridHeight; y++) {
                        if (walkable[x][y]) {
                            Point2D point = toActualPoint(new GridPoint(x, y));
                            walkableQuadtree.insert(point);
                        }
                    }
                }
            }

            private void initializeRTrees() {
                obstacleRTree = new RTree();
                shopRTree = new RTree();

                Floor floor = building.getFloor(floorNum);
                if (floor == null)
                    return;

                // 构建障碍物R-tree索引
                for (Obstacle obs : floor.getObstacles()) {
                    Rectangle2D bounds = getPolygonBounds(obs.getPolygon());
                    obstacleRTree.insert(bounds, obs);
                }

                // 构建商铺R-tree索引
                for (Node shop : building.getShops()) {
                    if (shop.getFloor() == floorNum && shop.getPolygon() != null) {
                        Rectangle2D bounds = getPolygonBounds(shop.getPolygon());
                        shopRTree.insert(bounds, shop);
                    }
                }

                System.out.println("楼层 " + floorNum + " R-tree索引构建完成");
            }

            // 检查点是否可行走（在通道内且不在障碍物内）
            private boolean isPointWalkable(Point2D point) {
                // 检查是否在通道多边形内
                if (!isPointInPolygon(point, polygon)) {
                    return false;
                }

                // 使用R-tree快速检测障碍物碰撞
                if (obstacleRTree != null) {
                    Rectangle2D pointBounds = new Rectangle2D.Double(point.getX() - 0.1, point.getY() - 0.1, 0.2, 0.2);
                    List<Object> nearbyObstacles = obstacleRTree.query(pointBounds);

                    for (Object obj : nearbyObstacles) {
                        if (obj instanceof Obstacle) {
                            Obstacle obs = (Obstacle) obj;
                            if (isPointInPolygon(point, obs.getPolygon())) {
                                return false;
                            }
                        }
                    }
                }

                // 使用R-tree快速检测商铺碰撞
                if (shopRTree != null) {
                    Rectangle2D pointBounds = new Rectangle2D.Double(point.getX() - 0.1, point.getY() - 0.1, 0.2, 0.2);
                    List<Object> nearbyShops = shopRTree.query(pointBounds);

                    for (Object obj : nearbyShops) {
                        if (obj instanceof Node) {
                            Node shop = (Node) obj;
                            if (shop.getPolygon() != null && isPointInPolygon(point, shop.getPolygon())) {
                                return false;
                            }
                        }
                    }
                }

                return true;
            }

            // 判断点是否在多边形内
            private boolean isPointInPolygon(Point2D point, List<Point2D> polygon) {
                int intersections = 0;
                for (int i = 0, j = polygon.size() - 1; i < polygon.size(); j = i++) {
                    Point2D p1 = polygon.get(j);
                    Point2D p2 = polygon.get(i);

                    if (((p1.getY() > point.getY()) != (p2.getY() > point.getY())) &&
                            (point.getX() < (p2.getX() - p1.getX()) *
                                    (point.getY() - p1.getY()) / (p2.getY() - p1.getY()) + p1.getX())) {
                        intersections++;
                    }
                }
                return (intersections % 2) == 1;
            }

            // 计算多边形的边界框
            private Rectangle2D getPolygonBounds(List<Point2D> polygon) {
                double minX = Double.MAX_VALUE, minY = Double.MAX_VALUE;
                double maxX = Double.MIN_VALUE, maxY = Double.MIN_VALUE;

                for (Point2D p : polygon) {
                    minX = Math.min(minX, p.getX());
                    minY = Math.min(minY, p.getY());
                    maxX = Math.max(maxX, p.getX());
                    maxY = Math.max(maxY, p.getY());
                }

                return new Rectangle2D.Double(minX, minY, maxX - minX, maxY - minY);
            }

            // 坐标转换
            public GridPoint toGridPoint(double x, double y) {
                int gridX = (int) ((x - minX) / gridSize);
                int gridY = (int) ((y - minY) / gridSize);
                return new GridPoint(gridX, gridY);
            }

            public Point2D toActualPoint(GridPoint gridPoint) {
                double x = minX + gridPoint.x * gridSize;
                double y = minY + gridPoint.y * gridSize;
                return new Point2D.Double(x, y);
            }
        }

        // 网格点类
        class GridPoint {
            public int x, y;

            public GridPoint(int x, int y) {
                this.x = x;
                this.y = y;
            }

            @Override
            public boolean equals(Object obj) {
                if (this == obj)
                    return true;
                if (obj == null || getClass() != obj.getClass())
                    return false;
                GridPoint that = (GridPoint) obj;
                return x == that.x && y == that.y;
            }

            @Override
            public int hashCode() {
                return Objects.hash(x, y);
            }
        }

        // 网格节点类（用于优先队列）
        class GridNode implements Comparable<GridNode> {
            GridPoint point;
            double fScore;

            public GridNode(GridPoint point, double fScore) {
                this.point = point;
                this.fScore = fScore;
            }

            @Override
            public int compareTo(GridNode other) {
                return Double.compare(this.fScore, other.fScore);
            }
        }

        // R-tree空间索引实现
        static class RTree {
            private static final int MAX_ENTRIES = 8;
            private static final int MIN_ENTRIES = 3;

            private RTreeNode root;

            public RTree() {
                this.root = new RTreeLeaf();
            }

            public void insert(Rectangle2D bounds, Object data) {
                RTreeNode newRoot = root.insert(bounds, data);
                if (newRoot != null) {
                    // 根节点分裂，创建新根
                    RTreeInternal newRootInternal = new RTreeInternal();
                    newRootInternal.addChild(root);
                    newRootInternal.addChild(newRoot);
                    this.root = newRootInternal;
                }
            }

            public List<Object> query(Rectangle2D queryBounds) {
                List<Object> results = new ArrayList<>();
                root.query(queryBounds, results);
                return results;
            }

            public boolean intersects(Rectangle2D bounds) {
                return root.intersects(bounds);
            }

            // R-tree节点抽象类
            abstract static class RTreeNode {
                protected Rectangle2D bounds;

                public RTreeNode() {
                    this.bounds = null;
                }

                abstract RTreeNode insert(Rectangle2D bounds, Object data);

                abstract void query(Rectangle2D queryBounds, List<Object> results);

                abstract boolean intersects(Rectangle2D queryBounds);

                abstract boolean isLeaf();

                protected Rectangle2D expandBounds(Rectangle2D current, Rectangle2D newBounds) {
                    if (current == null)
                        return new Rectangle2D.Double(newBounds.getX(), newBounds.getY(),
                                newBounds.getWidth(), newBounds.getHeight());

                    double minX = Math.min(current.getMinX(), newBounds.getMinX());
                    double minY = Math.min(current.getMinY(), newBounds.getMinY());
                    double maxX = Math.max(current.getMaxX(), newBounds.getMaxX());
                    double maxY = Math.max(current.getMaxY(), newBounds.getMaxY());

                    return new Rectangle2D.Double(minX, minY, maxX - minX, maxY - minY);
                }
            }

            // R-tree叶子节点
            static class RTreeLeaf extends RTreeNode {
                private List<RTreeEntry> entries;

                public RTreeLeaf() {
                    this.entries = new ArrayList<>();
                }

                @Override
                RTreeNode insert(Rectangle2D bounds, Object data) {
                    entries.add(new RTreeEntry(bounds, data));
                    this.bounds = expandBounds(this.bounds, bounds);

                    if (entries.size() > MAX_ENTRIES) {
                        return split();
                    }
                    return null;
                }

                @Override
                void query(Rectangle2D queryBounds, List<Object> results) {
                    for (RTreeEntry entry : entries) {
                        if (entry.bounds.intersects(queryBounds)) {
                            results.add(entry.data);
                        }
                    }
                }

                @Override
                boolean intersects(Rectangle2D queryBounds) {
                    if (bounds == null || !bounds.intersects(queryBounds)) {
                        return false;
                    }

                    for (RTreeEntry entry : entries) {
                        if (entry.bounds.intersects(queryBounds)) {
                            return true;
                        }
                    }
                    return false;
                }

                @Override
                boolean isLeaf() {
                    return true;
                }

                private RTreeLeaf split() {
                    // 简单的线性分裂算法
                    RTreeLeaf newLeaf = new RTreeLeaf();

                    // 找到最远的两个条目
                    int seed1 = 0, seed2 = 1;
                    double maxDistance = 0;

                    for (int i = 0; i < entries.size(); i++) {
                        for (int j = i + 1; j < entries.size(); j++) {
                            double distance = calculateDistance(entries.get(i).bounds, entries.get(j).bounds);
                            if (distance > maxDistance) {
                                maxDistance = distance;
                                seed1 = i;
                                seed2 = j;
                            }
                        }
                    }

                    // 分配种子
                    newLeaf.entries.add(entries.get(seed2));
                    newLeaf.bounds = entries.get(seed2).bounds;

                    List<RTreeEntry> remainingEntries = new ArrayList<>(entries);
                    remainingEntries.remove(seed2);
                    remainingEntries.remove(seed1);

                    this.entries.clear();
                    this.entries.add(entries.get(seed1));
                    this.bounds = entries.get(seed1).bounds;

                    // 分配剩余条目
                    for (RTreeEntry entry : remainingEntries) {
                        double area1 = calculateAreaIncrease(this.bounds, entry.bounds);
                        double area2 = calculateAreaIncrease(newLeaf.bounds, entry.bounds);

                        if (area1 < area2 || (area1 == area2 && this.entries.size() < newLeaf.entries.size())) {
                            this.entries.add(entry);
                            this.bounds = expandBounds(this.bounds, entry.bounds);
                        } else {
                            newLeaf.entries.add(entry);
                            newLeaf.bounds = expandBounds(newLeaf.bounds, entry.bounds);
                        }
                    }

                    return newLeaf;
                }

                private double calculateDistance(Rectangle2D r1, Rectangle2D r2) {
                    double dx = r1.getCenterX() - r2.getCenterX();
                    double dy = r1.getCenterY() - r2.getCenterY();
                    return Math.sqrt(dx * dx + dy * dy);
                }

                private double calculateAreaIncrease(Rectangle2D current, Rectangle2D newBounds) {
                    Rectangle2D expanded = expandBounds(current, newBounds);
                    return expanded.getWidth() * expanded.getHeight() - current.getWidth() * current.getHeight();
                }
            }

            // R-tree内部节点
            static class RTreeInternal extends RTreeNode {
                private List<RTreeNode> children;

                public RTreeInternal() {
                    this.children = new ArrayList<>();
                }

                public void addChild(RTreeNode child) {
                    children.add(child);
                    this.bounds = expandBounds(this.bounds, child.bounds);
                }

                @Override
                RTreeNode insert(Rectangle2D bounds, Object data) {
                    // 选择最佳子节点
                    RTreeNode bestChild = chooseSubtree(bounds);
                    RTreeNode newNode = bestChild.insert(bounds, data);

                    this.bounds = expandBounds(this.bounds, bounds);

                    if (newNode != null) {
                        children.add(newNode);
                        if (children.size() > MAX_ENTRIES) {
                            return split();
                        }
                    }
                    return null;
                }

                @Override
                void query(Rectangle2D queryBounds, List<Object> results) {
                    for (RTreeNode child : children) {
                        if (child.bounds != null && child.bounds.intersects(queryBounds)) {
                            child.query(queryBounds, results);
                        }
                    }
                }

                @Override
                boolean intersects(Rectangle2D queryBounds) {
                    if (bounds == null || !bounds.intersects(queryBounds)) {
                        return false;
                    }

                    for (RTreeNode child : children) {
                        if (child.intersects(queryBounds)) {
                            return true;
                        }
                    }
                    return false;
                }

                @Override
                boolean isLeaf() {
                    return false;
                }

                private RTreeNode chooseSubtree(Rectangle2D bounds) {
                    RTreeNode best = children.get(0);
                    double minIncrease = calculateAreaIncrease(best.bounds, bounds);

                    for (int i = 1; i < children.size(); i++) {
                        RTreeNode child = children.get(i);
                        double increase = calculateAreaIncrease(child.bounds, bounds);
                        if (increase < minIncrease) {
                            minIncrease = increase;
                            best = child;
                        }
                    }

                    return best;
                }

                private double calculateAreaIncrease(Rectangle2D current, Rectangle2D newBounds) {
                    if (current == null)
                        return newBounds.getWidth() * newBounds.getHeight();
                    Rectangle2D expanded = expandBounds(current, newBounds);
                    return expanded.getWidth() * expanded.getHeight() - current.getWidth() * current.getHeight();
                }

                private RTreeInternal split() {
                    // 简化的分裂实现
                    RTreeInternal newInternal = new RTreeInternal();

                    int mid = children.size() / 2;
                    List<RTreeNode> newChildren = new ArrayList<>(children.subList(mid, children.size()));
                    children = new ArrayList<>(children.subList(0, mid));

                    // 重新计算边界
                    this.bounds = null;
                    for (RTreeNode child : children) {
                        this.bounds = expandBounds(this.bounds, child.bounds);
                    }

                    for (RTreeNode child : newChildren) {
                        newInternal.addChild(child);
                    }

                    return newInternal;
                }
            }

            // R-tree条目
            static class RTreeEntry {
                Rectangle2D bounds;
                Object data;

                public RTreeEntry(Rectangle2D bounds, Object data) {
                    this.bounds = bounds;
                    this.data = data;
                }
            }
        }

        // 四叉树实现
        class Quadtree {
            private static final int CAPACITY = 4;
            private final double x, y, width, height;
            private final List<Point2D> points = new ArrayList<>();
            private Quadtree[] children;

            public Quadtree(double x, double y, double width, double height) {
                this.x = x;
                this.y = y;
                this.width = width;
                this.height = height;
            }

            public boolean insert(Point2D point) {
                if (!contains(point))
                    return false;

                if (children == null && points.size() < CAPACITY) {
                    points.add(point);
                    return true;
                }

                if (children == null) {
                    subdivide();
                }

                for (Quadtree child : children) {
                    if (child.insert(point)) {
                        return true;
                    }
                }

                return false;
            }

            public List<Point2D> query(double minX, double minY, double maxX, double maxY) {
                List<Point2D> result = new ArrayList<>();

                if (!intersects(minX, minY, maxX, maxY)) {
                    return result;
                }

                for (Point2D point : points) {
                    if (point.getX() >= minX && point.getX() <= maxX &&
                            point.getY() >= minY && point.getY() <= maxY) {
                        result.add(point);
                    }
                }

                if (children != null) {
                    for (Quadtree child : children) {
                        result.addAll(child.query(minX, minY, maxX, maxY));
                    }
                }

                return result;
            }

            private boolean contains(Point2D point) {
                return point.getX() >= x && point.getX() <= x + width &&
                        point.getY() >= y && point.getY() <= y + height;
            }

            private boolean intersects(double minX, double minY, double maxX, double maxY) {
                return !(maxX < x || minX > x + width || maxY < y || minY > y + height);
            }

            private void subdivide() {
                children = new Quadtree[4];
                double halfWidth = width / 2;
                double halfHeight = height / 2;

                children[0] = new Quadtree(x, y, halfWidth, halfHeight);
                children[1] = new Quadtree(x + halfWidth, y, halfWidth, halfHeight);
                children[2] = new Quadtree(x, y + halfHeight, halfWidth, halfHeight);
                children[3] = new Quadtree(x + halfWidth, y + halfHeight, halfWidth, halfHeight);

                // 将现有点重新插入
                for (Point2D point : points) {
                    for (Quadtree child : children) {
                        if (child.insert(point)) {
                            break;
                        }
                    }
                }
                points.clear();
            }
        }
    }

    // 获取最优路径和备选路线
    public static NavigationResult getRoutes(int startFloor, double startX, double startY,
            int endFloor, double endX, double endY) throws Exception {
        if (!isInitialized) {
            throw new IllegalStateException("导航系统未初始化，请先调用initialize()方法");
        }

        // 如果起点和终点在同一楼层，尝试使用网格路径规划
        if (startFloor == endFloor) {
            // 获取或创建网格路径规划器
            GridPathPlanner gridPlanner = plannerCache.computeIfAbsent(building, k -> new GridPathPlanner(building));

            // 使用网格路径规划
            List<Point2D> gridPath = gridPlanner.findPathOnGrid(startFloor, startX, startY, endX, endY);

            if (gridPath != null && !gridPath.isEmpty()) {
                // 将网格路径转换为Node路径
                Path path = new Path();
                List<Node> nodes = new ArrayList<>();

                for (Point2D point : gridPath) {
                    // 创建临时节点表示路径点
                    Node node = new Node(
                            "grid_point_" + point.getX() + "_" + point.getY(),
                            startFloor,
                            point.getX(),
                            point.getY(),
                            NodeType.PASSAGE,
                            null);
                    nodes.add(node);
                }

                path.setNodes(nodes);
                // 计算路径总长度
                double totalDistance = calculatePathDistance(nodes);
                path.setTotalDistance(totalDistance);

                return new NavigationResult(path, Collections.emptyList());
            } else {
                System.out.println("网格路径规划失败，回退到节点路径规划");
            }
        }

        // 如果网格路径规划失败，回退到原来的节点路径规划
        // 寻找最近的通道节点作为起点和终点
        Node start = findNearestAccessibleNode(startFloor, startX, startY);
        Node end = findNearestAccessibleNode(endFloor, endX, endY);

        if (start == null || end == null) {
            throw new IllegalArgumentException("在指定位置附近找不到可用的通道节点");
        }

        System.out.println("使用节点路径规划: " + start.getId() + " -> " + end.getId());

        Path optimalPath = pathAlgorithm.findPath(building, start, end);
        if (optimalPath == null) {
            throw new Exception("无法找到从 (" + startX + "," + startY + ") 到 (" + endX + "," + endY + ") 的路径");
        }

        // 移除路径最后的非连接节点（但保留垂直交通节点）
        List<Node> pathNodes = optimalPath.getNodes();
        if (!pathNodes.isEmpty() &&
                !isVerticalTransition(pathNodes.get(pathNodes.size() - 1)) &&
                pathNodes.get(pathNodes.size() - 1).getType() != NodeType.PASSAGE) {

            Node lastConnection = findLastConnectionNode(pathNodes);
            if (lastConnection != null) {
                int lastIndex = pathNodes.indexOf(lastConnection);
                if (lastIndex >= 0 && lastIndex < pathNodes.size() - 1) {
                    pathNodes = pathNodes.subList(0, lastIndex + 1);
                    optimalPath.setNodes(pathNodes);
                }
            }
        }

        double startToNode = calculateDistance(startX, startY, start);
        double nodeToEnd = calculateDistance(endX, endY, end);
        double adjustedTotal = optimalPath.getTotalDistance() + startToNode + nodeToEnd;
        optimalPath.setTotalDistance(adjustedTotal);

        // 使用深拷贝的建筑副本查找备选路径
        Building buildingCopy = deepCopyBuilding(building);
        List<Path> alternativePaths = new AlternativePathFinder()
                .findAlternativePaths(buildingCopy, start, end, 3, 1.5);

        for (Path altPath : alternativePaths) {
            double altTotal = altPath.getTotalDistance() + startToNode + nodeToEnd;
            altPath.setTotalDistance(altTotal);
        }

        return new NavigationResult(optimalPath, alternativePaths);
    }

    // 计算路径总长度
    private static double calculatePathDistance(List<Node> nodes) {
        double distance = 0;
        for (int i = 0; i < nodes.size() - 1; i++) {
            Node current = nodes.get(i);
            Node next = nodes.get(i + 1);
            distance += Math.sqrt(
                    Math.pow(next.getX() - current.getX(), 2) +
                            Math.pow(next.getY() - current.getY(), 2));
        }
        return distance;
    }

    // 深拷贝建筑对象
    private static Building deepCopyBuilding(Building original) {
        if (original == null)
            return null;

        Building copy = new Building();

        for (Floor floor : original.floors) {
            Floor newFloor = new Floor(floor.getNumber());
            for (Obstacle obstacle : floor.getObstacles()) {
                List<Point2D> newPolygon = new ArrayList<>(obstacle.getPolygon());
                newFloor.addObstacle(new Obstacle(obstacle.getType(), newPolygon));
            }
            copy.addFloor(newFloor);
        }

        // 复制节点
        for (Node node : original.nodes) {
            Node newNode = new Node(
                    node.getId(),
                    node.getFloor(),
                    node.getX(),
                    node.getY(),
                    node.getType(),
                    node.getConnectedId());
            newNode.setName(node.getName());
            if (node.getPolygon() != null) {
                newNode.setPolygon(new ArrayList<>(node.getPolygon()));
            }
            copy.addNode(newNode);
        }

        // 复制连接关系
        for (String nodeId : original.graph.keySet()) {
            Map<String, Double> connections = original.graph.get(nodeId);
            if (connections != null) {
                copy.graph.put(nodeId, new HashMap<>(connections));
            }
        }

        return copy;
    }

    // 判断是否为垂直交通节点
    private static boolean isVerticalTransition(Node node) {
        return node != null && (node.getType() == NodeType.ESCALATOR ||
                node.getType() == NodeType.ELEVATOR ||
                node.getType() == NodeType.STAIR);
    }

    private static Node findLastConnectionNode(List<Node> pathNodes) {
        for (int i = pathNodes.size() - 1; i >= 0; i--) {
            Node node = pathNodes.get(i);
            if (isVerticalTransition(node) || node.getType() == NodeType.PASSAGE) {
                return node;
            }
        }
        return null;
    }

    private static double calculateDistance(double x, double y, Node node) {
        return Math.sqrt(Math.pow(x - node.getX(), 2) + Math.pow(y - node.getY(), 2));
    }

    public static Node findNearestAccessibleNode(int floorNum, double x, double y) {
        List<Node> floorNodes = building.getNodesOnFloor(floorNum);

        // 优先选择通道节点
        List<Node> passageNodes = floorNodes.stream()
                .filter(node -> node.getType() == NodeType.PASSAGE)
                .collect(Collectors.toList());

        // 如果没有通道节点，则考虑其他可访问节点
        List<Node> accessibleNodes = passageNodes.isEmpty() ? floorNodes.stream()
                .filter(node -> node.getType() == NodeType.ESCALATOR ||
                        node.getType() == NodeType.STAIR ||
                        node.getType() == NodeType.ELEVATOR)
                .collect(Collectors.toList()) : passageNodes;

        if (accessibleNodes.isEmpty()) {
            return null;
        }

        // 找到最近的节点
        Node nearest = null;
        double minDistance = Double.MAX_VALUE;

        for (Node node : accessibleNodes) {
            double distance = Math.sqrt(Math.pow(node.getX() - x, 2) + Math.pow(node.getY() - y, 2));
            if (distance < minDistance) {
                minDistance = distance;
                nearest = node;
            }
        }

        return nearest;
    }

    // 收集坐标范围
    private static void collectCoordinateRange(JsonNode floorNode,
            double[] minLon, double[] maxLon, double[] minLat, double[] maxLat) {

        // 收集障碍物坐标
        for (JsonNode obstacleNode : floorNode.path("obstacles")) {
            JsonNode coords = obstacleNode.path("coordinates");
            for (int i = 0; i < coords.size(); i++) {
                JsonNode coord = coords.get(i);
                double lon = coord.get(0).asDouble();
                double lat = coord.get(1).asDouble();

                minLon[0] = Math.min(minLon[0], lon);
                maxLon[0] = Math.max(maxLon[0], lon);
                minLat[0] = Math.min(minLat[0], lat);
                maxLat[0] = Math.max(maxLat[0], lat);
            }
        }

        // 收集店铺坐标
        for (JsonNode shopNode : floorNode.path("shops")) {
            JsonNode coords = shopNode.path("coordinates");
            for (int i = 0; i < coords.size(); i++) {
                JsonNode coord = coords.get(i);
                double lon = coord.get(0).asDouble();
                double lat = coord.get(1).asDouble();

                minLon[0] = Math.min(minLon[0], lon);
                maxLon[0] = Math.max(maxLon[0], lon);
                minLat[0] = Math.min(minLat[0], lat);
                maxLat[0] = Math.max(maxLat[0], lat);
            }
        }

        // 收集通道坐标
        for (JsonNode passageNode : floorNode.path("passages")) {
            JsonNode coords = passageNode.path("coordinates");
            for (int i = 0; i < coords.size(); i++) {
                JsonNode coord = coords.get(i);
                double lon = coord.get(0).asDouble();
                double lat = coord.get(1).asDouble();

                minLon[0] = Math.min(minLon[0], lon);
                maxLon[0] = Math.max(maxLon[0], lon);
                minLat[0] = Math.min(minLat[0], lat);
                maxLat[0] = Math.max(maxLat[0], lat);
            }
        }

        // 收集扶梯坐标
        for (JsonNode escalatorNode : floorNode.path("escalators")) {
            JsonNode coords = escalatorNode.path("coordinates");
            for (int i = 0; i < coords.size(); i++) {
                JsonNode coord = coords.get(i);
                double lon = coord.get(0).asDouble();
                double lat = coord.get(1).asDouble();

                minLon[0] = Math.min(minLon[0], lon);
                maxLon[0] = Math.max(maxLon[0], lon);
                minLat[0] = Math.min(minLat[0], lat);
                maxLat[0] = Math.max(maxLat[0], lat);
            }
        }
    }

    private static Building loadBuildingData(String resourcePath) throws IOException {
        Building b = new Building();
        ObjectMapper mapper = new ObjectMapper();

        // 读取文件一次
        JsonNode root;
        try (InputStream is = IndoorNavigationSystem3D.class.getResourceAsStream(resourcePath)) {
            if (is == null) {
                throw new IOException("建筑数据文件未找到: " + resourcePath);
            }
            root = mapper.readTree(is);
        }

        // 使用root进行实际加载
        JsonNode floors = root.path("floors");
        for (JsonNode floorNode : floors) {
            int floorNum = floorNode.path("floor").asInt();
            Floor floor = new Floor(floorNum);

            // 解析障碍物
            for (JsonNode obstacleNode : floorNode.path("obstacles")) {
                String type = obstacleNode.path("type").asText();
                List<Point2D> polygon = new ArrayList<>();

                JsonNode coords = obstacleNode.path("coordinates");
                for (int i = 0; i < coords.size(); i++) {
                    JsonNode coord = coords.get(i);
                    double x = coord.get(0).asDouble(); // 直接使用X坐标
                    double y = coord.get(1).asDouble(); // 直接使用Y坐标
                    polygon.add(new Point2D.Double(x, y));
                }

                Obstacle obstacle = new Obstacle(type, polygon);
                floor.addObstacle(obstacle);
            }

            // 解析房间（店铺）
            for (JsonNode shopNode : floorNode.path("shops")) {
                String id = shopNode.path("id").asText();
                String name = shopNode.path("name").asText();

                List<Point2D> polygon = new ArrayList<>();
                JsonNode coords = shopNode.path("coordinates");
                for (int i = 0; i < coords.size(); i++) {
                    JsonNode coord = coords.get(i);
                    double x = coord.get(0).asDouble(); // 直接使用X坐标
                    double y = coord.get(1).asDouble(); // 直接使用Y坐标
                    polygon.add(new Point2D.Double(x, y));
                }

                Point2D center = calculateCentroid(polygon);
                Node shopNodeObj = new Node(id, floorNum, center.getX(), center.getY(), NodeType.SHOP, null);
                shopNodeObj.setName(name);
                shopNodeObj.setPolygon(polygon);
                b.addShop(shopNodeObj);
                b.addNode(shopNodeObj);
            }

            // 解析扶梯
            for (JsonNode escalatorNode : floorNode.path("escalators")) {
                String id = escalatorNode.path("id").asText();
                String connectedId = escalatorNode.path("connectedId").asText();

                List<Point2D> polygon = new ArrayList<>();
                JsonNode coords = escalatorNode.path("coordinates");
                for (int i = 0; i < coords.size(); i++) {
                    JsonNode coord = coords.get(i);
                    double x = coord.get(0).asDouble(); // 直接使用X坐标
                    double y = coord.get(1).asDouble(); // 直接使用Y坐标
                    polygon.add(new Point2D.Double(x, y));
                }

                Point2D center = calculateCentroid(polygon);
                Node escalator = new Node(id, floorNum, center.getX(), center.getY(), NodeType.ESCALATOR, connectedId);
                escalator.setPolygon(polygon);
                b.addNode(escalator);
            }

            // 解析通道节点
            for (JsonNode passageNode : floorNode.path("passages")) {
                String id = passageNode.path("id").asText();

                List<Point2D> polygon = new ArrayList<>();
                JsonNode coords = passageNode.path("coordinates");
                for (int i = 0; i < coords.size(); i++) {
                    JsonNode coord = coords.get(i);
                    double x = coord.get(0).asDouble(); // 直接使用X坐标
                    double y = coord.get(1).asDouble(); // 直接使用Y坐标
                    polygon.add(new Point2D.Double(x, y));
                }

                Point2D center = calculateCentroid(polygon);
                Node passage = new Node(id, floorNum, center.getX(), center.getY(), NodeType.PASSAGE, null);
                passage.setPolygon(polygon);
                b.addNode(passage);
            }

            b.addFloor(floor);
        }

        b.verifyConnections();
        b.createRealisticConnections();

        return b;
    }

    // 计算多边形中心点
    private static Point2D calculateCentroid(List<Point2D> points) {
        double centroidX = 0;
        double centroidY = 0;
        for (Point2D p : points) {
            centroidX += p.getX();
            centroidY += p.getY();
        }
        centroidX /= points.size();
        centroidY /= points.size();
        return new Point2D.Double(centroidX, centroidY);
    }

    // 导航结果封装类
    public static class NavigationResult {
        private final Path optimalPath;
        private final List<Path> alternativePaths;

        public NavigationResult(Path optimalPath, List<Path> alternativePaths) {
            this.optimalPath = optimalPath;
            this.alternativePaths = alternativePaths;
        }

        public Path getOptimalPath() {
            return optimalPath;
        }

        public List<Path> getAlternativePaths() {
            return alternativePaths;
        }
    }

    // 建筑模型类
    static class Building {
        private final List<Floor> floors = new ArrayList<>();
        private final List<Node> nodes = new ArrayList<>();
        private final List<Node> shops = new ArrayList<>();
        private final Map<String, Map<String, Double>> graph = new HashMap<>();

        public void addFloor(Floor floor) {
            floors.add(floor);
        }

        public void verifyConnections() {
            for (Node node : nodes) {
                Map<String, Double> connections = graph.get(node.getId());
                if (connections == null || connections.isEmpty()) {
                    System.err.println("警告: 节点 " + node.getId() + " 无有效连接");
                }
            }
        }

        public Floor getFloor(int floorNumber) {
            for (Floor floor : floors) {
                if (floor.getNumber() == floorNumber) {
                    return floor;
                }
            }
            return null;
        }

        public void addNode(Node node) {
            nodes.add(node);
            graph.putIfAbsent(node.getId(), new HashMap<>());
        }

        public void addShop(Node shop) {
            shops.add(shop);
        }

        public List<Floor> getFloors() {
            return floors;
        }

        public List<Node> getNodes() {
            return nodes;
        }

        public List<Node> getShops() {
            return shops;
        }

        public List<Node> getNodesOnFloor(int floorNum) {
            return nodes.stream()
                    .filter(node -> node.getFloor() == floorNum)
                    .collect(Collectors.toList());
        }

        // 创建实际可行的连接
        public void createRealisticConnections() {
            // 连接同一楼层的节点
            for (Floor floor : floors) {
                List<Node> floorNodes = getNodesOnFloor(floor.getNumber());
                connectPassageNodes(floor, floorNodes);
                connectShopsToPassages(floor, floorNodes);
            }

            connectVerticalNodes();
            connectVerticalNodesToPassages();
        }

        private void connectVerticalNodesToPassages() {
            for (Floor floor : floors) {
                int floorNum = floor.getNumber();
                List<Node> floorNodes = getNodesOnFloor(floorNum);

                List<Node> verticalNodes = floorNodes.stream()
                        .filter(node -> node.getType() == NodeType.STAIR ||
                                node.getType() == NodeType.ELEVATOR ||
                                node.getType() == NodeType.ESCALATOR)
                        .collect(Collectors.toList());

                List<Node> passageNodes = floorNodes.stream()
                        .filter(node -> node.getType() == NodeType.PASSAGE)
                        .collect(Collectors.toList());

                // 连接垂直节点到通道节点
                for (Node verticalNode : verticalNodes) {
                    for (Node passage : passageNodes) {
                        double dist = distance(verticalNode, passage);
                        if (dist <= 50) { // 减少连接距离阈值
                            addConnection(verticalNode.getId(), passage.getId(), dist);
                        }
                    }
                }
            }
        }

        private void connectPassageNodes(Floor floor, List<Node> floorNodes) {
            List<Node> passageNodes = floorNodes.stream()
                    .filter(node -> node.getType() == NodeType.PASSAGE)
                    .collect(Collectors.toList());

            // 连接相邻的通道节点
            for (int i = 0; i < passageNodes.size(); i++) {
                Node a = passageNodes.get(i);
                for (int j = i + 1; j < passageNodes.size(); j++) {
                    Node b = passageNodes.get(j);
                    double dist = Math.sqrt(Math.pow(b.getX() - a.getX(), 2) + Math.pow(b.getY() - a.getY(), 2));

                    if (dist <= 50 && !isPathBlocked(a, b, floor)) { // 减少连接距离阈值
                        addConnection(a.getId(), b.getId(), dist);
                    }
                }
            }
        }

        private void connectShopsToPassages(Floor floor, List<Node> floorNodes) {
            List<Node> shopNodes = floorNodes.stream()
                    .filter(node -> node.getType() == NodeType.SHOP)
                    .collect(Collectors.toList());

            List<Node> passageNodes = floorNodes.stream()
                    .filter(node -> node.getType() == NodeType.PASSAGE)
                    .collect(Collectors.toList());

            // 连接每个店铺到最近的通道节点
            for (Node shop : shopNodes) {
                Node nearestPassage = null;
                double minDist = Double.MAX_VALUE;

                for (Node passage : passageNodes) {
                    double dist = Math.sqrt(
                            Math.pow(shop.getX() - passage.getX(), 2) +
                                    Math.pow(shop.getY() - passage.getY(), 2));
                    if (dist < minDist && dist < 50 && !isPathBlocked(shop, passage, floor)) { // 减少连接距离阈值
                        minDist = dist;
                        nearestPassage = passage;
                    }
                }

                if (nearestPassage != null) {
                    addConnection(shop.getId(), nearestPassage.getId(), minDist);
                }
            }
        }

        private void connectVerticalNodes() {
            for (Node node : nodes) {
                if (node.getConnectedId() != null) {
                    Node connectedNode = findNodeById(node.getConnectedId());
                    if (connectedNode != null) {
                        // 使用楼层高度常量计算垂直距离
                        double floorDistance = Math.abs(node.getFloor() - connectedNode.getFloor()) * FLOOR_HEIGHT;
                        double planeDistance = Math.sqrt(
                                Math.pow(node.getX() - connectedNode.getX(), 2) +
                                        Math.pow(node.getY() - connectedNode.getY(), 2));
                        double totalDistance = floorDistance + planeDistance;

                        addConnection(node.getId(), connectedNode.getId(), totalDistance);
                        addConnection(connectedNode.getId(), node.getId(), totalDistance);
                    }
                }
            }
        }

        // 检测两点间是否有障碍物
        public boolean isPathBlocked(Node a, Node b, Floor floor) {
            // 垂直交通节点不检测障碍物
            if (isVerticalTransition(a) || isVerticalTransition(b)) {
                return false;
            }

            Line2D line = new Line2D.Double(a.getX(), a.getY(), b.getX(), b.getY());

            // 检查是否与任何障碍物相交
            for (Obstacle obs : floor.getObstacles()) {
                if (lineIntersectsPolygon(line, obs.getPolygon())) {
                    return true;
                }
            }

            // 检查是否与任何商店相交
            for (Node shop : getShopsOnFloor(floor.getNumber())) {
                if (shop.getPolygon() != null && lineIntersectsPolygon(line, shop.getPolygon())) {
                    return true;
                }
            }

            return false;
        }

        // 获取指定楼层的所有商店
        private List<Node> getShopsOnFloor(int floorNum) {
            return shops.stream()
                    .filter(shop -> shop.getFloor() == floorNum)
                    .collect(Collectors.toList());
        }

        // 判断线段是否与多边形相交
        private boolean lineIntersectsPolygon(Line2D line, List<Point2D> polygon) {
            Rectangle2D polygonBounds = getPolygonBounds(polygon);
            if (!polygonBounds.intersectsLine(line)) {
                return false;
            }

            for (int i = 0; i < polygon.size(); i++) {
                Point2D p1 = polygon.get(i);
                Point2D p2 = polygon.get((i + 1) % polygon.size());
                Line2D edge = new Line2D.Double(p1, p2);

                if (line.intersectsLine(edge)) {
                    return true;
                }
            }

            if (isPointInPolygon(new Point2D.Double(line.getX1(), line.getY1()), polygon) ||
                    isPointInPolygon(new Point2D.Double(line.getX2(), line.getY2()), polygon)) {
                return true;
            }

            return false;
        }

        // 计算多边形的边界框
        private Rectangle2D getPolygonBounds(List<Point2D> polygon) {
            double minX = Double.MAX_VALUE, minY = Double.MAX_VALUE;
            double maxX = Double.MIN_VALUE, maxY = Double.MIN_VALUE;

            for (Point2D p : polygon) {
                minX = Math.min(minX, p.getX());
                minY = Math.min(minY, p.getY());
                maxX = Math.max(maxX, p.getX());
                maxY = Math.max(maxY, p.getY());
            }

            return new Rectangle2D.Double(minX, minY, maxX - minX, maxY - minY);
        }

        // 射线法判断点是否在多边形内
        private boolean isPointInPolygon(Point2D point, List<Point2D> polygon) {
            int intersections = 0;
            for (int i = 0, j = polygon.size() - 1; i < polygon.size(); j = i++) {
                Point2D p1 = polygon.get(j);
                Point2D p2 = polygon.get(i);

                if (((p1.getY() > point.getY()) != (p2.getY() > point.getY())) &&
                        (point.getX() < (p2.getX() - p1.getX()) *
                                (point.getY() - p1.getY()) / (p2.getY() - p1.getY()) + p1.getX())) {
                    intersections++;
                }
            }
            return (intersections % 2) == 1;
        }

        private void addConnection(String fromId, String toId, double distance) {
            graph.computeIfAbsent(fromId, k -> new HashMap<>()).put(toId, distance);
            graph.computeIfAbsent(toId, k -> new HashMap<>()).put(fromId, distance);
        }

        public Path findShortestPath(Node start, Node end) {
            return pathAlgorithm.findPath(this, start, end);
        }

        public Node findNodeById(String id) {
            return nodes.stream()
                    .filter(node -> node.getId().equals(id))
                    .findFirst()
                    .orElse(null);
        }

        private double distance(Node a, Node b) {
            if (a.getFloor() != b.getFloor()) {
                return Double.MAX_VALUE;
            }
            return Math.sqrt(Math.pow(b.getX() - a.getX(), 2) + Math.pow(b.getY() - a.getY(), 2));
        }
    }

    // 3D A*算法实现
    static class AStar3DAlgorithm implements PathfindingAlgorithm {
        @Override
        public Path findPath(Building building, Node start, Node end) {
            Map<String, Double> gScore = new HashMap<>();
            Map<String, Double> fScore = new HashMap<>();
            Map<String, String> cameFrom = new HashMap<>();
            PriorityQueue<NodeScore> openSet = new PriorityQueue<>();

            gScore.put(start.getId(), 0.0);
            fScore.put(start.getId(), heuristic(start, end));
            openSet.add(new NodeScore(start, fScore.get(start.getId())));

            while (!openSet.isEmpty()) {
                Node current = openSet.poll().node;

                if (current.equals(end)) {
                    return reconstructPath(cameFrom, current, gScore.get(current.getId()));
                }

                Map<String, Double> neighbors = building.graph.get(current.getId());
                if (neighbors == null)
                    continue;

                for (Map.Entry<String, Double> neighbor : neighbors.entrySet()) {
                    String neighborId = neighbor.getKey();
                    Node neighborNode = building.findNodeById(neighborId);
                    if (neighborNode == null)
                        continue;

                    // 检查从当前节点到邻居节点的路径是否被障碍物阻挡
                    if (current.getFloor() == neighborNode.getFloor()) {
                        Floor floor = building.getFloor(current.getFloor());
                        if (floor != null && building.isPathBlocked(current, neighborNode, floor)) {
                            continue; // 跳过被阻挡的路径
                        }
                    }

                    double tentativeG = gScore.get(current.getId()) + neighbor.getValue();

                    if (tentativeG < gScore.getOrDefault(neighborId, Double.MAX_VALUE)) {
                        cameFrom.put(neighborId, current.getId());
                        gScore.put(neighborId, tentativeG);
                        fScore.put(neighborId, tentativeG + heuristic(neighborNode, end));

                        if (!openSetContains(openSet, neighborNode)) {
                            openSet.add(new NodeScore(neighborNode, fScore.get(neighborId)));
                        }
                    }
                }
            }
            return null;
        }

        private double heuristic(Node a, Node b) {
            double floorPenalty = Math.abs(a.getFloor() - b.getFloor()) * FLOOR_HEIGHT * 1.5;

            if (a.getFloor() == b.getFloor()) {
                return Math.sqrt(Math.pow(b.getX() - a.getX(), 2) + Math.pow(b.getY() - a.getY(), 2));
            }

            return Math.abs(b.getX() - a.getX()) + Math.abs(b.getY() - a.getY()) + floorPenalty;
        }

        private boolean openSetContains(PriorityQueue<NodeScore> openSet, Node node) {
            return openSet.stream().anyMatch(ns -> ns.node.equals(node));
        }

        private Path reconstructPath(Map<String, String> cameFrom, Node end, double totalDistance) {
            LinkedList<Node> pathNodes = new LinkedList<>();
            String currentNodeId = end.getId();

            // 从终点回溯路径
            while (cameFrom.containsKey(currentNodeId)) {
                pathNodes.addFirst(building.findNodeById(currentNodeId));
                currentNodeId = cameFrom.get(currentNodeId);
            }

            // 添加起点
            pathNodes.addFirst(building.findNodeById(currentNodeId));

            Path path = new Path();
            path.setNodes(pathNodes);
            path.setTotalDistance(totalDistance);
            return path;
        }
    }

    // 障碍物类
    static class Obstacle {
        private final String type;
        private final List<Point2D> polygon;

        public Obstacle(String type, List<Point2D> polygon) {
            this.type = type;
            this.polygon = polygon;
        }

        public String getType() {
            return type;
        }

        public List<Point2D> getPolygon() {
            return polygon;
        }
    }

    // 节点类型枚举
    enum NodeType {
        SHOP, DOOR, STAIR, ELEVATOR, ESCALATOR, PASSAGE
    }

    // 节点类
    public static class Node {
        private final String id;
        private final int floor;
        private final double x;
        private final double y;
        private final NodeType type;
        private final String connectedId;
        private String name;
        private List<Point2D> polygon;

        public Node(String id, int floor, double x, double y, NodeType type, String connectedId) {
            this.id = id;
            this.floor = floor;
            this.x = x;
            this.y = y;
            this.type = type;
            this.connectedId = connectedId;
        }

        public String getId() {
            return id;
        }

        public int getFloor() {
            return floor;
        }

        public double getX() {
            return x;
        }

        public double getY() {
            return y;
        }

        public NodeType getType() {
            return type;
        }

        public String getConnectedId() {
            return connectedId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<Point2D> getPolygon() {
            return polygon;
        }

        public void setPolygon(List<Point2D> polygon) {
            this.polygon = polygon;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o)
                return true;
            if (o == null || getClass() != o.getClass())
                return false;
            Node node = (Node) o;
            return id.equals(node.id);
        }

        @Override
        public int hashCode() {
            return id.hashCode();
        }

        @Override
        public String toString() {
            return id + " (" + type + ")";
        }
    }

    // 路径类
    public static class Path {
        private double totalDistance;
        private List<Node> nodes = new ArrayList<>();

        public double getTotalDistance() {
            return totalDistance;
        }

        public void setTotalDistance(double totalDistance) {
            this.totalDistance = totalDistance;
        }

        public List<Node> getNodes() {
            return nodes;
        }

        public void setNodes(List<Node> nodes) {
            this.nodes = nodes;
        }

        public String getNodeSequence() {
            return nodes.stream().map(Node::getId).collect(Collectors.joining("->"));
        }
    }

    // 楼层类
    static class Floor {
        private final int number;
        private final List<Obstacle> obstacles = new ArrayList<>();

        public Floor(int number) {
            this.number = number;
        }

        public int getNumber() {
            return number;
        }

        public List<Obstacle> getObstacles() {
            return obstacles;
        }

        public void addObstacle(Obstacle obstacle) {
            obstacles.add(obstacle);
        }
    }

    // A*算法辅助类
    static class NodeScore implements Comparable<NodeScore> {
        Node node;
        double score;

        public NodeScore(Node node, double score) {
            this.node = node;
            this.score = score;
        }

        @Override
        public int compareTo(NodeScore other) {
            return Double.compare(this.score, other.score);
        }
    }

    // 路径规划算法接口
    interface PathfindingAlgorithm {
        Path findPath(Building building, Node start, Node end);
    }

    // 备选路线查找器
    static class AlternativePathFinder {
        public List<Path> findAlternativePaths(Building building, Node start, Node end,
                int numPaths, double diversityFactor) {
            List<Path> alternativePaths = new ArrayList<>();
            Path shortest = building.findShortestPath(start, end);
            if (shortest == null)
                return alternativePaths;

            if (shortest.getNodes().size() < 2) {
                alternativePaths.add(shortest);
                return alternativePaths;
            }

            Set<String> pathSignatures = new HashSet<>();
            pathSignatures.add(generatePathSignature(shortest));
            Set<String> brokenEdges = new HashSet<>();
            int maxAttempts = numPaths * 10;
            int attempts = 0;

            while (alternativePaths.size() < numPaths && attempts < maxAttempts) {
                attempts++;
                Path currentPath = building.findShortestPath(start, end);
                if (currentPath == null)
                    break;

                // 随机断开一条边
                Node a = null, b = null;
                String edgeSignature = null;

                if (currentPath.getNodes().size() > 1) {
                    int randomIndex = new Random().nextInt(currentPath.getNodes().size() - 1);
                    a = currentPath.getNodes().get(randomIndex);
                    b = currentPath.getNodes().get(randomIndex + 1);
                    edgeSignature = generateEdgeKey(a, b);

                    // 临时移除连接（但不永久改变建筑图）
                    building.graph.get(a.getId()).remove(b.getId());
                    building.graph.get(b.getId()).remove(a.getId());
                }

                Path newPath = building.findShortestPath(start, end);

                // 恢复连接
                if (a != null && b != null) {
                    building.graph.get(a.getId()).put(b.getId(), distance(a, b));
                    building.graph.get(b.getId()).put(a.getId(), distance(a, b));
                }

                if (newPath != null) {
                    String signature = generatePathSignature(newPath);
                    if (!pathSignatures.contains(signature) &&
                            newPath.getTotalDistance() <= shortest.getTotalDistance() * diversityFactor) {
                        alternativePaths.add(newPath);
                        pathSignatures.add(signature);
                    }
                }
            }

            alternativePaths.sort(Comparator.comparingDouble(Path::getTotalDistance));
            return alternativePaths;
        }

        private double distance(Node a, Node b) {
            if (a.getFloor() != b.getFloor()) {
                return Math.abs(a.getFloor() - b.getFloor()) * FLOOR_HEIGHT +
                        Math.sqrt(Math.pow(b.getX() - a.getX(), 2) + Math.pow(b.getY() - a.getY(), 2));
            }
            return Math.sqrt(Math.pow(b.getX() - a.getX(), 2) + Math.pow(b.getY() - a.getY(), 2));
        }

        private String generateEdgeKey(Node a, Node b) {
            return a.getId().compareTo(b.getId()) < 0 ? a.getId() + "-" + b.getId() : b.getId() + "-" + a.getId();
        }

        private String generatePathSignature(Path path) {
            return path.getNodes().stream()
                    .map(node -> node.getId() + "(" + node.getFloor() + ")")
                    .collect(Collectors.joining("->"));
        }
    }

    // 使用向量优化路径点生成
    public static List<RouteCoordinateVo> generatePathPoints(Path path, double startX, double startY, double endX,
            double endY) {
        if (path == null || path.getNodes() == null || path.getNodes().isEmpty()) {
            return Collections.emptyList();
        }

        List<Node> nodes = path.getNodes();
        List<RouteCoordinateVo> routePoints = new ArrayList<>();

        // 添加起点
        addPoint(routePoints, startX, startY);

        // 检查是否是网格路径（节点ID以"grid_point_"开头）
        if (!nodes.isEmpty() && nodes.get(0).getId().startsWith("grid_point_")) {
            // 网格路径直接使用所有节点
            for (Node node : nodes) {
                addPoint(routePoints, node.getX(), node.getY());
            }
        } else {
            // 原来的节点路径处理逻辑
            // 处理起点到第一个节点
            if (!nodes.isEmpty()) {
                Node firstNode = nodes.get(0);
                interpolatePoints(new Point2D.Double(startX, startY),
                        new Point2D.Double(firstNode.getX(), firstNode.getY()),
                        routePoints);
            }

            // 处理中间节点
            for (int i = 0; i < nodes.size() - 1; i++) {
                Node current = nodes.get(i);
                Node next = nodes.get(i + 1);
                addPoint(routePoints, current.getX(), current.getY());
                interpolatePoints(new Point2D.Double(current.getX(), current.getY()),
                        new Point2D.Double(next.getX(), next.getY()),
                        routePoints);
            }

            // 处理最后一个节点到终点
            if (!nodes.isEmpty()) {
                Node lastNode = nodes.get(nodes.size() - 1);
                addPoint(routePoints, lastNode.getX(), lastNode.getY());
                interpolatePoints(new Point2D.Double(lastNode.getX(), lastNode.getY()),
                        new Point2D.Double(endX, endY),
                        routePoints);
            }
        }

        // 添加终点
        addPoint(routePoints, endX, endY);

        return routePoints;
    }

    private static void interpolatePoints(Point2D from, Point2D to, List<RouteCoordinateVo> points) {
        double dx = to.getX() - from.getX();
        double dy = to.getY() - from.getY();
        double distance = Math.sqrt(dx * dx + dy * dy);

        if (distance <= 0)
            return;

        int segments = (int) Math.floor(distance);
        double stepX = dx / segments;
        double stepY = dy / segments;

        // 每隔1米添加点
        for (int i = 1; i <= segments; i++) {
            double x = from.getX() + i * stepX;
            double y = from.getY() + i * stepY;
            addPoint(points, x, y);
        }
    }

    private static void addPoint(List<RouteCoordinateVo> points, double x, double y) {
        RouteCoordinateVo point = new RouteCoordinateVo();
        point.setX(x);
        point.setY(y);
        points.add(point);
    }

    // 路径点封装对象
    public static class RouteCoordinateVo {
        private double x;
        private double y;
        private Integer floorNum;
        private String type;

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Integer getFloorNum() {
            return floorNum;
        }

        public void setFloorNum(Integer floorNum) {
            this.floorNum = floorNum;
        }

        public double getX() {
            return x;
        }

        public void setX(double x) {
            this.x = x;
        }

        public double getY() {
            return y;
        }

        public void setY(double y) {
            this.y = y;
        }
    }

    // 性能测试方法
    public void performanceTest() {
        try {
            System.out.println("=== 室内导航系统性能测试 ===");

            // 测试多个路径规划请求
            double[][] testCases = {
                    { 13.0, 13.0, 1, 15.0, 15.0, 1 }, // 短距离
                    { 10.0, 10.0, 1, 20.0, 20.0, 1 }, // 中距离
                    { 5.0, 5.0, 1, 25.0, 25.0, 1 }, // 长距离
            };

            for (int i = 0; i < testCases.length; i++) {
                double[] testCase = testCases[i];
                System.out.println("\n--- 测试案例 " + (i + 1) + " ---");

                long startTime = System.currentTimeMillis();

                NavigationResult result = getRoutes(
                        (int) testCase[2], testCase[0], testCase[1],
                        (int) testCase[5], testCase[3], testCase[4]);

                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;

                System.out.printf("起点: (%.1f, %.1f, %d) -> 终点: (%.1f, %.1f, %d)\n",
                        testCase[0], testCase[1], (int) testCase[2],
                        testCase[3], testCase[4], (int) testCase[5]);
                System.out.println("路径规划耗时: " + duration + "ms");

                if (result != null && result.getOptimalPath() != null) {
                    System.out.println("找到最优路径");
                    Path optimalPath = result.getOptimalPath();
                    System.out.printf("最优路径: %d 个节点, 长度: %.2f 米\n",
                            optimalPath.getNodes().size(), optimalPath.getTotalDistance());

                    List<Path> alternatives = result.getAlternativePaths();
                    if (alternatives != null && !alternatives.isEmpty()) {
                        System.out.println("备选路径: " + alternatives.size() + " 条");
                        for (int j = 0; j < alternatives.size(); j++) {
                            Path altPath = alternatives.get(j);
                            System.out.printf("备选路径 %d: %d 个节点, 长度: %.2f 米\n",
                                    j + 1, altPath.getNodes().size(), altPath.getTotalDistance());
                        }
                    }
                } else {
                    System.out.println("未找到路径");
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 计算路径长度
    private double calculatePathLength(List<Point2D> path) {
        if (path.size() < 2)
            return 0.0;

        double totalLength = 0.0;
        for (int i = 1; i < path.size(); i++) {
            Point2D p1 = path.get(i - 1);
            Point2D p2 = path.get(i);
            double dx = p2.getX() - p1.getX();
            double dy = p2.getY() - p1.getY();
            totalLength += Math.sqrt(dx * dx + dy * dy);
        }

        return totalLength;
    }

    // 主方法用于测试
    public static void main(String[] args) {
        try {
            IndoorNavigationSystem3D system = new IndoorNavigationSystem3D();
            IndoorNavigationSystem3D.loadBuildingData("pages/index/test.json");

            // 运行性能测试
            system.performanceTest();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
