# 室内导航系统路径规划优化 - 架构设计文档

## 整体架构设计

### 系统分层架构

```mermaid
graph TB
    A[API层 - getRoutes方法] --> B[路径规划协调器]
    B --> C[空间索引层]
    B --> D[网格算法层]
    B --> E[路径平滑层]

    C --> C1[R-tree索引]
    C --> C2[空间查询引擎]

    D --> D1[自适应网格生成器]
    D --> D2[改进A*算法]
    D --> D3[层次化网格]

    E --> E1[Douglas-Peucker简化]
    E --> E2[贝塞尔曲线平滑]
    E --> E3[约束保持器]

    F[数据层] --> G[Building/Floor/Node]
    F --> H[Obstacle/Shop多边形]
```

### 核心组件设计

#### 1. 空间索引组件 (SpatialIndexManager)

- **R-tree索引**: 管理障碍物和商铺的空间索引
- **快速查询**: 支持范围查询和碰撞检测
- **动态更新**: 支持索引的增量更新

#### 2. 网格算法组件 (AdaptiveGridPlanner)

- **自适应网格**: 根据区域复杂度动态调整网格大小
- **层次化结构**: 多层次网格提升性能
- **改进A***: 优化启发式函数和搜索策略

#### 3. 路径平滑组件 (PathSmoother)

- **路径简化**: Douglas-Peucker算法减少冗余点
- **曲线平滑**: 贝塞尔曲线生成自然路径
- **约束检查**: 确保平滑后路径不违反避障约束

## 模块依赖关系

```mermaid
graph LR
    A[IndoorNavigationSystem3D] --> B[SpatialIndexManager]
    A --> C[AdaptiveGridPlanner]
    A --> D[PathSmoother]

    C --> B
    D --> B

    B --> E[RTree]
    C --> F[GridFloor]
    D --> G[PathOptimizer]
```

## 接口契约定义

### SpatialIndexManager接口

```java
public interface SpatialIndexManager {
    void buildIndex(List<Obstacle> obstacles, List<Node> shops);
    List<Obstacle> queryObstacles(Rectangle2D bounds);
    List<Node> queryShops(Rectangle2D bounds);
    boolean isPointBlocked(Point2D point);
    boolean isLineBlocked(Line2D line);
}
```

### AdaptiveGridPlanner接口

```java
public interface AdaptiveGridPlanner {
    GridFloor createAdaptiveGrid(Floor floor, List<Point2D> passagePolygon);
    List<Point2D> findOptimalPath(GridFloor grid, Point2D start, Point2D end);
    double calculateOptimalGridSize(double area, int obstacleCount);
}
```

### PathSmoother接口

```java
public interface PathSmoother {
    List<Point2D> simplifyPath(List<Point2D> path, double tolerance);
    List<Point2D> smoothPath(List<Point2D> path, SpatialIndexManager spatialIndex);
    boolean validateSmoothedPath(List<Point2D> path, SpatialIndexManager spatialIndex);
}
```

## 数据流向设计

```mermaid
sequenceDiagram
    participant API as getRoutes()
    participant Coord as 路径协调器
    participant Spatial as 空间索引
    participant Grid as 网格规划器
    participant Smooth as 路径平滑器

    API->>Coord: 请求路径规划
    Coord->>Spatial: 构建空间索引
    Spatial-->>Coord: 索引就绪

    Coord->>Grid: 创建自适应网格
    Grid->>Spatial: 查询障碍物
    Spatial-->>Grid: 返回障碍物列表
    Grid-->>Coord: 网格就绪

    Coord->>Grid: 执行A*路径搜索
    Grid-->>Coord: 返回原始路径

    Coord->>Smooth: 路径平滑处理
    Smooth->>Spatial: 验证平滑约束
    Spatial-->>Smooth: 约束检查结果
    Smooth-->>Coord: 返回平滑路径

    Coord-->>API: 返回最终路径
```

## 异常处理策略

### 1. 空间索引异常

- **索引构建失败**: 回退到原始空间哈希方法
- **查询超时**: 使用缓存结果或简化查询
- **内存不足**: 动态调整索引参数

### 2. 网格算法异常

- **网格生成失败**: 回退到固定网格大小
- **A*搜索失败**: 回退到节点路径规划
- **内存溢出**: 动态减小网格精度

### 3. 路径平滑异常

- **平滑失败**: 返回原始路径
- **约束违反**: 回退到简化路径
- **计算超时**: 使用部分平滑结果

## 性能优化策略

### 1. 缓存策略

- **网格缓存**: 缓存已生成的网格结构
- **索引缓存**: 缓存空间索引查询结果
- **路径缓存**: 缓存常用路径的计算结果

### 2. 并行处理

- **异步索引构建**: 后台构建空间索引
- **并行网格生成**: 多线程生成不同区域网格
- **分段平滑**: 并行处理路径的不同段

### 3. 内存管理

- **延迟加载**: 按需加载网格数据
- **内存池**: 重用网格和路径对象
- **垃圾回收优化**: 减少临时对象创建
