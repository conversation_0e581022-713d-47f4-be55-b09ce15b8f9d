package com.ruoyi.ai.scheduler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.mail.*;
import javax.mail.internet.MimeMessage;
import java.util.Properties;

/**
 * 邮箱配置测试工具
 * 用于独立测试邮箱连接配置是否正确
 */
@Slf4j
@Component
public class EmailConfigTester {

    @Autowired
    private MultiEmailMonitor multiEmailMonitor;

    /**
     * 测试邮箱连接配置
     * @param host IMAP服务器地址
     * @param port IMAP端口
     * @param username 用户名
     * @param password 密码/授权码
     * @return 测试结果
     */
    public TestResult testConnection(String host, String port, String username, String password) {
        TestResult result = new TestResult();
        result.setHost(host);
        result.setPort(port);
        result.setUsername(username);
        
        try {
            log.info("开始测试邮箱连接: {}@{}", username, host);
            
            // 基础参数验证
            if (host == null || host.trim().isEmpty()) {
                result.setSuccess(false);
                result.setErrorMessage("IMAP服务器地址不能为空");
                return result;
            }
            
            if (port == null || port.trim().isEmpty()) {
                result.setSuccess(false);
                result.setErrorMessage("IMAP端口不能为空");
                return result;
            }
            
            if (username == null || username.trim().isEmpty()) {
                result.setSuccess(false);
                result.setErrorMessage("用户名不能为空");
                return result;
            }
            
            if (password == null || password.trim().isEmpty()) {
                result.setSuccess(false);
                result.setErrorMessage("密码/授权码不能为空");
                return result;
            }
            
            // 端口号验证
            int portNum;
            try {
                portNum = Integer.parseInt(port);
                if (portNum < 1 || portNum > 65535) {
                    result.setSuccess(false);
                    result.setErrorMessage("端口号必须在1-65535范围内");
                    return result;
                }
            } catch (NumberFormatException e) {
                result.setSuccess(false);
                result.setErrorMessage("端口号格式错误");
                return result;
            }
            
            // 创建邮件会话属性
            Properties props = createMailProperties(host, port);
            
            // 尝试连接
            Session session = Session.getInstance(props, null);
            Store store = session.getStore("imap");
            
            long startTime = System.currentTimeMillis();
            store.connect(host, portNum, username, password);
            long connectTime = System.currentTimeMillis() - startTime;
            
            if (store.isConnected()) {
                // 尝试访问INBOX
                Folder inbox = store.getFolder("INBOX");
                if (inbox.exists()) {
                    inbox.open(Folder.READ_ONLY);
                    int messageCount = inbox.getMessageCount();
                    inbox.close(false);
                    
                    result.setSuccess(true);
                    result.setConnectTime(connectTime);
                    result.setMessageCount(messageCount);
                    result.setMessage("连接成功！邮箱中共有 " + messageCount + " 封邮件");
                    
                    log.info("邮箱连接测试成功: {}@{}, 耗时: {}ms, 邮件数: {}", 
                            username, host, connectTime, messageCount);
                } else {
                    result.setSuccess(false);
                    result.setErrorMessage("INBOX文件夹不存在");
                }
                
                store.close();
            } else {
                result.setSuccess(false);
                result.setErrorMessage("连接失败：未能建立有效连接");
            }
            
        } catch (AuthenticationFailedException e) {
            result.setSuccess(false);
            result.setErrorMessage("认证失败：用户名或密码错误");
            result.setSuggestion(getSuggestionForHost(host));
            log.error("邮箱认证失败: {}@{}", username, host, e);
        } catch (MessagingException e) {
            result.setSuccess(false);
            result.setErrorMessage("连接失败：" + e.getMessage());
            log.error("邮箱连接失败: {}@{}", username, host, e);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage("未知错误：" + e.getMessage());
            log.error("邮箱测试异常: {}@{}", username, host, e);
        }
        
        return result;
    }
    
    /**
     * 创建邮件属性配置
     */
    private Properties createMailProperties(String host, String port) {
        Properties props = new Properties();
        props.put("mail.store.protocol", "imap");
        props.put("mail.imap.host", host);
        props.put("mail.imap.port", port);
        props.put("mail.imap.connectiontimeout", "30000");
        props.put("mail.imap.timeout", "60000");
        props.put("mail.imap.writetimeout", "40000");
        
        // 根据邮箱类型设置特殊配置
        if (host.contains("163.com")) {
            props.put("mail.imap.auth.plain.disable", "true");
            props.put("mail.imap.auth.login.disable", "false");
            props.put("mail.imap.ssl.checkserveridentity", "false");
            props.put("mail.imap.ssl.trust", "*");
        } else if (host.contains("qq.com")) {
            props.put("mail.imap.ssl.checkserveridentity", "false");
            props.put("mail.imap.ssl.trust", "*");
        }
        
        // SSL配置
        if ("993".equals(port)) {
            props.put("mail.imap.ssl.enable", "true");
            props.put("mail.imap.ssl.protocols", "TLSv1.2");
        } else if ("143".equals(port)) {
            props.put("mail.imap.starttls.enable", "true");
            props.put("mail.imap.starttls.required", "true");
        }
        
        return props;
    }
    
    /**
     * 根据邮箱服务商提供建议
     */
    private String getSuggestionForHost(String host) {
        if (host.contains("163.com")) {
            return "163邮箱建议：1. 使用授权码而非登录密码；2. 在网页邮箱中启用IMAP服务；3. 检查账号安全设置";
        } else if (host.contains("qq.com")) {
            return "QQ邮箱建议：1. 使用授权码而非QQ密码；2. 在QQ邮箱设置中开启IMAP/SMTP服务";
        } else if (host.contains("gmail.com")) {
            return "Gmail建议：1. 使用应用专用密码；2. 启用两步验证；3. 确保IMAP访问已开启";
        } else if (host.contains("outlook.com") || host.contains("hotmail.com")) {
            return "Outlook建议：1. 使用应用密码；2. 检查两步验证设置；3. 确认IMAP配置正确";
        }
        return "建议检查：1. 用户名和密码是否正确；2. IMAP服务是否已启用；3. 网络连接是否正常";
    }
    
    /**
     * 测试结果类
     */
    public static class TestResult {
        private boolean success;
        private String host;
        private String port;
        private String username;
        private String message;
        private String errorMessage;
        private String suggestion;
        private long connectTime;
        private int messageCount;
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getHost() { return host; }
        public void setHost(String host) { this.host = host; }
        
        public String getPort() { return port; }
        public void setPort(String port) { this.port = port; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public String getSuggestion() { return suggestion; }
        public void setSuggestion(String suggestion) { this.suggestion = suggestion; }
        
        public long getConnectTime() { return connectTime; }
        public void setConnectTime(long connectTime) { this.connectTime = connectTime; }
        
        public int getMessageCount() { return messageCount; }
        public void setMessageCount(int messageCount) { this.messageCount = messageCount; }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("=== 邮箱连接测试结果 ===\n");
            sb.append("服务器: ").append(host).append(":").append(port).append("\n");
            sb.append("用户名: ").append(username).append("\n");
            sb.append("结果: ").append(success ? "✅ 成功" : "❌ 失败").append("\n");
            
            if (success) {
                sb.append("连接耗时: ").append(connectTime).append("ms\n");
                sb.append("邮件数量: ").append(messageCount).append("\n");
                if (message != null) {
                    sb.append("详情: ").append(message).append("\n");
                }
            } else {
                if (errorMessage != null) {
                    sb.append("错误: ").append(errorMessage).append("\n");
                }
                if (suggestion != null) {
                    sb.append("建议: ").append(suggestion).append("\n");
                }
            }
            
            return sb.toString();
        }
    }
}
