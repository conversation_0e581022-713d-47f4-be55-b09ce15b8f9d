# 室内导航系统路径规划优化 - 对齐文档

## 项目上下文分析

### 现有项目结构

- **主要文件**: `pages/index/IndoorNavigationSystem3D.java`
- **数据文件**: `pages/index/test.json`
- **技术栈**: Java + Jackson JSON + 几何计算库
- **架构模式**: 3D室内导航系统，使用A*算法进行路径规划

### 现有代码模式分析

1. **数据模型**:
   - Building（建筑）→ Floor（楼层）→ Obstacles/Shops/Passages
   - Node节点系统：SHOP, PASSAGE, ESCALATOR, ELEVATOR, STAIR
   - 使用多边形(Polygon)表示空间区域

2. **路径规划策略**:
   - 网格化路径规划（GridPathPlanner）
   - 节点路径规划（AStar3DAlgorithm）
   - 双重回退机制：网格失败→节点规划

3. **障碍物检测**:
   - 射线法判断点在多边形内
   - 空间哈希加速碰撞检测
   - 线段与多边形相交检测

## 原始需求理解

### 核心需求

优化IndoorNavigationSystem3D.java的路径规划方法，确保：

1. **空间索引优化** - 提升碰撞检测性能
2. **网格算法优化** - 改进路径质量和精度
3. **路径平滑实现** - 让路径更自然流畅
4. **避障保证** - 路径不穿过商铺和障碍物

### 边界确认

- **范围**: 仅优化IndoorNavigationSystem3D.java文件
- **数据**: 基于现有test.json数据格式
- **兼容性**: 保持现有API接口不变
- **性能**: 提升路径规划速度和质量

## 技术约束和集成方案

### 技术约束

1. **Java语言限制**: 使用Java标准库和现有依赖
2. **内存限制**: 大型建筑网格内存占用需控制
3. **实时性要求**: 路径规划响应时间<500ms
4. **精度要求**: 路径精度误差<0.5米

### 集成方案

1. **向后兼容**: 保持现有getRoutes()方法签名
2. **渐进式优化**: 可独立启用/禁用各项优化
3. **配置化**: 关键参数可配置调整
4. **监控**: 添加性能监控和日志

## 疑问澄清

### 已通过代码分析解决的疑问

1. **坐标系统**: 使用Web Mercator投影(EPSG:3857)
2. **网格大小**: 当前根据区域面积动态计算(0.5-2.0米)
3. **障碍物类型**: 主要是COLUMN类型的柱子障碍物
4. **商铺表示**: 使用多边形坐标表示商铺边界

### 需要确认的技术决策

1. **R-tree vs 四叉树**: 选择R-tree作为空间索引结构
2. **网格策略**: 采用自适应网格+层次化网格
3. **平滑算法**: 使用Douglas-Peucker+贝塞尔曲线组合
4. **缓存策略**: 缓存网格和空间索引以提升性能

## 验收标准

### 功能验收

- [ ] 路径不穿过任何商铺多边形区域
- [ ] 路径不穿过任何障碍物多边形区域
- [ ] 路径在通道(passage)区域内行走
- [ ] 支持多楼层路径规划

### 性能验收

- [ ] 路径规划时间<500ms（大型建筑）
- [ ] 内存占用相比原版减少30%
- [ ] 碰撞检测性能提升50%

### 质量验收

- [ ] 路径平滑度提升，减少锯齿状路径
- [ ] 路径长度接近最优解（误差<10%）
- [ ] 路径自然度提升，符合人类行走习惯

## 项目特性规范

### 空间索引特性

- 使用R-tree索引障碍物和商铺
- 支持快速范围查询和碰撞检测
- 动态更新索引结构

### 网格算法特性

- 自适应网格大小（0.2-2.0米）
- 层次化网格结构
- 改进的A*启发式函数

### 路径平滑特性

- Douglas-Peucker路径简化
- 贝塞尔曲线平滑处理
- 保持避障约束的平滑
