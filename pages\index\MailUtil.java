package com.ruoyi.ai.utils.mail;

import com.ruoyi.ai.domain.*;
import com.ruoyi.ai.scheduler.EmailTaskConfig;
import com.ruoyi.ai.service.IAiEmailClickService;
import com.ruoyi.common.utils.OssUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysAccount;
import com.sun.mail.imap.IMAPStore;
import com.sun.mail.smtp.SMTPSendFailedException;
import jdk.nashorn.internal.runtime.logging.Logger;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.*;
import javax.mail.internet.*;
import javax.mail.search.ComparisonTerm;
import javax.mail.search.ReceivedDateTerm;
import javax.mail.search.SearchTerm;
import javax.mail.search.SentDateTerm;
import java.io.*;
import java.net.InetAddress;
import java.net.URL;
import java.net.URLConnection;
import java.net.UnknownHostException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;


/**
 * 使用POP3协议接收邮件
 */
@Slf4j
@Service
@Configuration
@Logger
public class MailUtil {

    @Value("${email.track_url}")
    private String trackUrl;

    @Value("${email.redirect_url}")
    private String redirectUrl;

    @Autowired
    private IAiEmailClickService aiEmailClickService;



    /**
     * 发送邮件
     */
    public String sendEmail(String username, String password, String host, String port, String to,
                            String subject, String body, String showName, List<AiEmailAttachments> materialList) throws MessagingException, UnsupportedEncodingException {

        // 参数验证
        validateEmailParams(username, password, host, port, to);

        String trackingId = UUID.randomUUID().toString();
        log.info("trackingId->{}",trackingId);
        try {
            //创建邮件会话
            Session session = createMailSession(host, port, username, password);
            //构建MimeMessage对象
            MimeMessage message = buildMimeMessage(username, session, to,
                    subject, body, showName, materialList, trackingId,null);
            Transport.send(message, message.getAllRecipients());
            return trackingId;
        } catch (SMTPSendFailedException e){
            if(e.getValidSentAddresses().length == 0){
                throw new RuntimeException("邮件发送失败！");
            }
            log.error("发送成功邮箱：{}",Arrays.toString(e.getValidSentAddresses()));

            return trackingId;
        }
    }

    public String sendEmail(AiEmail task, EmailTaskConfig config) throws MessagingException, UnsupportedEncodingException {
        //邮件配置类
        AiEmailConfig emailConfig = config.getConfig();
        //邮件配置类
        AiEmailType emailType = config.getEmailType();
        //用户个人信息
        SysAccount account = config.getAccount();

        // 参数验证
        validateEmailParams(emailConfig.getUsername(), emailConfig.getPassword(),
                emailType.getSmtpHost(), emailType.getSmtpPort(),
                task.getRecipientEmailAddress());

        String trackingId = UUID.randomUUID().toString();
        log.info("trackingId->{}",trackingId);
        boolean anySuccess = false;
        try {
            //创建邮件会话
            Session session = createMailSession(emailType.getSmtpHost(), emailType.getSmtpPort(),
                    emailConfig.getUsername(), emailConfig.getPassword());
            //构建MimeMessage对象
            MimeMessage message = buildMimeMessage(emailConfig.getUsername(), session, task.getRecipientEmailAddress(),
                    task.getEmailSubject(), task.getEmailContent(),
                    account.getNickName(),task.getMaterialList(),trackingId,
                    account.getOfficialWebsite());
            Transport.send(message, message.getAllRecipients());
            anySuccess = true;
        } catch (SendFailedException e) {    // 部分或全部失败
            Address[] validSent = e.getValidSentAddresses();
            anySuccess = validSent != null && validSent.length > 0;
            log.warn("部分地址发送失败, 已发数量={}", anySuccess ? validSent.length : 0);
        }

        if (anySuccess) {
            if (StringUtils.isNotBlank(account.getOfficialWebsite()) &&
                    task.getEmailContent().contains(account.getOfficialWebsite())) {
                AiEmailClick click = new AiEmailClick();
                click.setTrackId(trackingId);
                click.setClickType("1");
                click.setCreateBy(account.getUserId());
                aiEmailClickService.insertAiEmailClick(click);
            }
        }
        return trackingId;
    }

    /**
     * 构建MimeMessage对象
     */
    private MimeMessage buildMimeMessage(String username, Session session, String to,
                            String subject, String body, String showName,
                            List<AiEmailAttachments> materialList, String trackingId,
                            String officialWebsite)
            throws MessagingException, UnsupportedEncodingException {
        MimeMessage  message = new MimeMessage(session);
        // 设置发件人，这里添加显示名称，（虚拟发件人），指定字符编码
        message.setFrom(new InternetAddress(username, showName, "UTF-8"));
        // 隔离回复路径
        message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(to));
        message.setSubject(subject);
        // 创建复合消息体
        Multipart multipart = new MimeMultipart();
        // 添加邮件正文
        body = addTrackingPixel(body, trackingId,officialWebsite);
        // 设置邮件ID用于后续状态检查
//        message.setHeader("X-Tracking-ID", trackingId);
        MimeBodyPart messageBodyPart = new MimeBodyPart();
        messageBodyPart.setContent(body, "text/html; charset=utf-8");
        multipart.addBodyPart(messageBodyPart);
        // 添加附件（如果有）
        addAttachments(materialList, multipart);
        // 设置邮件内容
        message.setContent(multipart);
        message.saveChanges();
        return message;
    }

    /**
     * 创建邮件会话
     */
    private Session createMailSession(String host, String port,String username, String password) {
        //创建SMTP配置
        Properties props = createSmtpProperties(host, port);

        return Session.getInstance(props, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(username, password);
            }
        });
    }

    /**
     * 添加附件
     */
    private void addAttachments(List<AiEmailAttachments> materialList, Multipart multipart) {
        if (materialList != null && !materialList.isEmpty()) {
            for (AiEmailAttachments attachment : materialList) {
                try {
                    MimeBodyPart attachmentBodyPart = new MimeBodyPart();
                    // 检查URL类型：本地文件还是网络资源
                    String url = attachment.getAttachmentUrl();
                    DataSource source;
                    if (url.startsWith("http:") || url.startsWith("https:")) {
                        // 处理网络资源：下载到临时文件
                        source = getDataSourceFromUrl(url);
                    } else {
                        // 本地文件
                        File file = new File(url);
                        if (file.exists()) {
                            source = new FileDataSource(file);
                        } else {
                            log.error("附件文件不存在: {}", url);
                            continue;
                        }
                    }
                    attachmentBodyPart.setDataHandler(new DataHandler(source));
                    attachmentBodyPart.setFileName(MimeUtility.encodeText(attachment.getAttachmentName(), "UTF-8", "B"));
                    multipart.addBodyPart(attachmentBodyPart);
                    log.info("添加附件: {}", attachment.getAttachmentName());
                } catch (Exception e) {
                    log.error("添加附件失败: {}", attachment.getAttachmentName(), e);
                }
            }
        }
    }

    /**
     * 创建SMTP配置
     */
    private Properties createSmtpProperties(String host, String port) {
        Properties props = new Properties();
        props.put("mail.smtp.host", host);
        props.put("mail.smtp.port", port);
        props.put("mail.smtp.auth", "true");
        // 根据端口号自动配置加密方式
        if ("465".equals(port)) {
            props.put("mail.smtp.ssl.enable", "true");
            props.put("mail.smtp.socketFactory.port", port);
            props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");

        } else if ("587".equals(port)) {
            props.put("mail.smtp.starttls.enable", "true");
        }
        props.put("mail.smtp.reportsuccess", "true");
        props.put("mail.smtp.connectiontimeout", "60000");
        props.put("mail.smtp.timeout", "60000");
        props.put("mail.smtp.writetimeout", "60000");
        props.put("mail.debug", "true");

        return props;
    }


    // 添加追踪像素到HTML
    private String addTrackingPixel(String htmlBody, String trackingId,String officialWebsite) {
        String pixelUrl = trackUrl + trackingId;
        String pixelTag = "<img src=\"" + pixelUrl + "\" width=\"1\" height=\"1\" style=\"display:none;\">";
        if (StringUtils.isNotBlank(officialWebsite) && htmlBody.contains(officialWebsite)){
            htmlBody = htmlBody.replace(officialWebsite,redirectUrl + trackingId);
        }
        if (htmlBody.contains("</body>")) {
            return htmlBody.replace("</body>", pixelTag + "</body>");
        } else {
            return htmlBody + pixelTag;
        }


    }

    public DataSource getDataSourceFromUrl(String urlString) throws IOException {
        URL url = new URL(urlString);
        URLConnection connection = url.openConnection();
        // 设置连接超时和读取超时
        connection.setConnectTimeout(10000);
        connection.setReadTimeout(30000);

        // 创建临时文件
        String prefix = "email_attach_";
        String suffix = getFileExtension(urlString);
        File tempFile = File.createTempFile(prefix, suffix);
        // 程序退出时删除临时文件
        tempFile.deleteOnExit();

        try (InputStream inputStream = connection.getInputStream();
             FileOutputStream outputStream = new FileOutputStream(tempFile)) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
        return new FileDataSource(tempFile);
    }

    // 从URL中获取文件扩展名
    private String getFileExtension(String url) {
        int pos = url.lastIndexOf('.');
        if (pos == -1) {
            return ".tmp";
        }
        return url.substring(pos);
    }

    public void validateEmailParams(String username, String password, String host, String port, String to) {
        if (!Arrays.asList("25", "465", "587").contains(port)) {
            throw new IllegalArgumentException("无效的端口号: " + port + "，支持25/465/587");
        }
        if (StringUtils.isNotBlank(username) && !username.contains("@")) {
            throw new IllegalArgumentException("无效的发件邮箱地址");
        }

//        boolean emailValid = EmailVerifier.isEmailValid(to);
        if (StringUtils.isNotBlank(to) && !to.contains("@")){
            throw new IllegalArgumentException("无效的收件邮箱地址");
        }
        if (password == null || password.trim().isEmpty()) {
            throw new IllegalArgumentException("密码/授权码不能为空");
        }
        if (host == null || host.trim().isEmpty()) {
            throw new IllegalArgumentException("无效的SMTP服务器: " + host);
        }
        if (!port.matches("\\d+")) {
            throw new IllegalArgumentException("无效的端口号: " + port);
        }
    }

    /**
     * 获取收邮件
     */
    public List<AiEmailData> receiveEmail(String servicePath, String port,
                                       String fromEmail, String password, Long userId) {

        if (fromEmail == null || fromEmail.isEmpty()) {
            throw new IllegalArgumentException("邮箱地址不能为空");
        }

        if (password == null || password.isEmpty()) {
            throw new IllegalArgumentException("授权码不能为空");
        }

        boolean is163Email = servicePath.contains("163.com");

        Properties props = createProperties(is163Email, servicePath, port);

        List<AiEmailData> aiEmailData = new ArrayList<>();
        IMAPStore store = null;
        Folder folder = null;

        try {
            Session session = Session.getInstance(props, null);
            log.debug("创建邮件会话成功");

            store = (IMAPStore) session.getStore("imap");
            log.debug("创建IMAP Store成功");

            if (is163Email) {
                // 163邮箱专用连接流程
                store.connect(servicePath, fromEmail, password);

                // 发送客户端ID提高信任度（安全关键）
                Map<String, String> clientID = new HashMap<>();
                clientID.put("name", "EnterpriseMailClient");
                clientID.put("version", "1.0.0");
                clientID.put("vendor", "YourCompany");
                clientID.put("support-email", "<EMAIL>");
                store.id(clientID);

                // 等待服务器处理身份信息（重要）
                log.debug("等待服务器处理客户端ID信息...");
                Thread.sleep(300);
            } else {
                // 其他邮箱的标准连接
                store.connect(servicePath, Integer.parseInt(port), fromEmail, password);
            }

            // 验证连接是否建立
            if (!store.isConnected()) {
                throw new MessagingException("IMAP连接失败: 未建立有效连接");
            }
            log.info("成功连接到邮箱服务器: {}:{}", servicePath, port);

            // ================== 打开INBOX文件夹 ==================
            folder = store.getFolder("INBOX");
            if (!folder.exists()) {
                throw new FolderNotFoundException();
            }

            int openAttempts = 0;
            final int MAX_OPEN_ATTEMPTS = 3;
            boolean openedSuccessfully = false;
            int finalOpenMode = Folder.READ_ONLY; // 默认使用只读模式

            while (openAttempts < MAX_OPEN_ATTEMPTS && !openedSuccessfully) {
                try {
                    openAttempts++;

                    // 163邮箱首选只读模式
                    int openMode = (is163Email && openAttempts < 2) ?
                            Folder.READ_ONLY : Folder.READ_WRITE;

                    String modeDesc = (openMode == Folder.READ_ONLY) ?
                            "只读(READ_ONLY)" : "读写(READ_WRITE)";

                    log.info("尝试打开文件夹[尝试 {}/{}] 模式={}",
                            openAttempts, MAX_OPEN_ATTEMPTS, modeDesc);

                    folder.open(openMode);
                    openedSuccessfully = true;
                    finalOpenMode = openMode;
                    log.info("文件夹成功打开, 模式={}", modeDesc);

                } catch (MessagingException e) {
                    log.warn("文件夹打开失败: {}", e.getMessage());

                    if (openAttempts >= MAX_OPEN_ATTEMPTS) {
                        // 添加163邮箱专用错误处理
                        if (e.getMessage().contains("Unsafe Login")) {
                            String advice = "163邮箱安全限制触发:\n"
                                    + "1. 请确保在网页版邮箱中开启了IMAP服务\n"
                                    + "2. 确保使用了授权码而非登录密码\n"
                                    + "3. 首次在此位置登录时，请在网页版邮箱中通过安全检查\n"
                                    + "4. 确认账号未处于异常状态";
                            log.error(advice);
                        }
                        throw e;
                    }

                    // 指数退避等待
                    long sleepTime = 1000L * (long)Math.pow(2, openAttempts);
                    log.info("等待 {}ms 后重试文件夹打开...", sleepTime);
                    Thread.sleep(sleepTime);
                }
            }

            // ================== 获取当天邮件 ==================
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date startOfDay = calendar.getTime();

            // 163邮箱使用SentDateTerm更可靠
            SearchTerm searchTerm = is163Email ?
                    new SentDateTerm(ComparisonTerm.GE, startOfDay) :
                    new ReceivedDateTerm(ComparisonTerm.GE, startOfDay);

            Message[] messages = folder.search(searchTerm);
            log.info("找到 {} 封符合条件的邮件", messages.length);

            // ================== 解析邮件 ==================
            final int parallelism = Math.min(20, Runtime.getRuntime().availableProcessors());
            ExecutorService executor = Executors.newWorkStealingPool(parallelism);
            int finalOpenMode1 = finalOpenMode;
            List<CompletableFuture<AiEmailData>> futures = Arrays.stream(messages)
                    .map(msg -> CompletableFuture.supplyAsync(
                            () -> parseSingleMessage(userId, (MimeMessage) msg),
                            executor))
                    .collect(Collectors.toList());
//            aiEmailData = parseMessage2(userId, messages, finalOpenMode != Folder.READ_ONLY);
            aiEmailData = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            executor.shutdown();
            log.info("成功解析 {} 封邮件", aiEmailData.size());

        } catch (AuthenticationFailedException e) {
            String errorMsg = "邮件认证失败: ";
            if (is163Email) {
                errorMsg += "\n可能原因: "
                        + "\n  1. 使用了登录密码而非授权码"
                        + "\n  2. IMAP服务未在网页邮箱启用"
                        + "\n  3. 账号未通过安全检查"
                        + "\n解决方案: "
                        + "\n  - 登录163邮箱网页版检查设置"
                        + "\n  - 重新获取授权码";
            } else {
                errorMsg += "请检查邮箱账号和授权码是否正确";
            }
            log.error(errorMsg, e);
            throw new RuntimeException(errorMsg);

        } catch (FolderNotFoundException e) {
            String errorMsg = "邮箱文件夹不存在: " + e.getMessage() +
                    "\n推荐设置: 确保邮箱账户存在INBOX文件夹";
            log.error(errorMsg, e);
            throw new RuntimeException(errorMsg);

        } catch (MessagingException e) {
            String errorDetail = "邮件操作失败: " + e.getMessage();

            // 163邮箱特殊错误处理
            if (e.getMessage().contains("Unsafe Login")) {
                errorDetail = "163邮箱安全限制:"
                        + "\n错误消息: " + e.getMessage()
                        + "\n解决方案: "
                        + "\n  1. 登录163邮箱网页版"
                        + "\n  2. 进入设置 → POP3/SMTP/IMAP"
                        + "\n  3. 开启IMAP服务并获取授权码"
                        + "\n  4. 确保使用的密码是授权码而非登录密码";
            }
            else if (e.getMessage().contains("disconnected")) {
                errorDetail = "连接意外断开: "
                        + "\n可能原因: "
                        + "\n  - 网络不稳定"
                        + "\n  - 服务器超时断开"
                        + "\n解决方案: 请检查网络连接后重试";
            }

            log.error(errorDetail, e);
            throw new RuntimeException(errorDetail);

        } catch (InterruptedException e) {
            log.error("线程被意外中断", e);
            Thread.currentThread().interrupt(); // 恢复中断状态
            throw new RuntimeException("邮件处理被中断");

        } catch (Exception e) {
            log.error("邮件处理发生未知错误", e);
            throw new RuntimeException("邮件处理失败: " + e.getMessage());

        } finally {
            // ================== 安全关闭资源 ==================
            closeFolderSafely(folder);
            closeStoreSafely(store);
        }

        return aiEmailData;
    }

    private Properties createProperties(boolean is163Email, String servicePath, String port){
        if (servicePath.isEmpty()) {
            throw new IllegalArgumentException("服务路径不能为空");
        }

        if (!Arrays.asList("993", "143").contains(port)) {
            throw new IllegalArgumentException("端口号必须为993(SSL)或143(非SSL)。你使用的是：" + port);
        }

        if (servicePath.startsWith("smtp.") && !servicePath.contains("163.com")) {
            // 如果是SMTP服务器地址，尝试转换为IMAP地址
            servicePath = servicePath.replace("smtp.", "imap.");
            log.warn("自动修正服务地址为: {}", servicePath);
        }

        Properties props = new Properties();
        props.put("mail.store.protocol", "imap");
        props.put("mail.imap.host", servicePath);
        props.put("mail.imap.port", port);

        props.put("mail.imap.connectiontimeout", "20000"); // 20秒连接超时
        props.put("mail.imap.timeout", "40000");            // 40秒操作超时
        props.put("mail.imap.writetimeout", "30000");       // 30秒写超时

        if (is163Email) {
            props.put("mail.imap.auth.plain.disable", "true");
            props.put("mail.imap.auth.login.disable", "false");
            props.put("mail.imap.ssl.checkserveridentity", "false");
            props.put("mail.imap.ssl.trust", "*");
            props.put("mail.imap.connectionpoolsize", "1"); // 减少连接池大小

            try {
                String localIP = InetAddress.getLocalHost().getHostAddress();
                props.put("mail.imap.localaddress", localIP);
            } catch (UnknownHostException ex) {
                log.warn("无法获取本地IP地址", ex);
            }
        }

        if ("993".equals(port)) {
            props.put("mail.imap.ssl.enable", "true");
            props.put("mail.imap.ssl.protocols", "TLSv1.2");
            props.put("mail.imap.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
            props.put("mail.imap.socketFactory.fallback", "false");
            props.put("mail.imap.socketFactory.port", port);
        } else if ("143".equals(port)) {
            props.put("mail.imap.starttls.enable", "true");
            props.put("mail.imap.starttls.required", "true");
        }

        return props;
    }

    private void closeFolderSafely(Folder folder) {
        if (folder != null) {
            try {
                if (folder.isOpen()) {
                    folder.close(false); // 关闭但不清除已删除消息
                    log.debug("文件夹已安全关闭");
                }
            } catch (MessagingException e) {
                log.warn("关闭文件夹时出错: {}", e.getMessage());
            }
        }
    }

    private void closeStoreSafely(Store store) {
        if (store != null) {
            try {
                if (store.isConnected()) {
                    store.close();
                    log.debug("存储连接已安全关闭");
                }
            } catch (MessagingException e) {
                log.warn("关闭存储连接时出错: {}", e.getMessage());
            }
        }
    }


    /**
     * 解析单封邮件（线程安全方法）
     */
    public static AiEmailData parseSingleMessage(Long userId, MimeMessage msg) {
        try {
            AiEmailData aiEmailData = new AiEmailData();

            // 基本信息解析（无需加载邮件体）
            aiEmailData.setMessageId(msg.getMessageID());
            aiEmailData.setTheme(getSubject3(msg));
            aiEmailData.setFromTime(parseSentDate(msg));
            aiEmailData.setIsRead(isSeen(msg) ? "1" : "0");
            aiEmailData.setPriority(getPriority(msg));
            aiEmailData.setIsReceipt(isReplySign(msg) ? "1" : "0");
            aiEmailData.setExtent(Math.max(1, msg.getSize() / 1024) + "kb");

            // 发件人/收件人信息
            Map<String, String> from = getFrom(msg);
            aiEmailData.setSender(from.get("from"));
            aiEmailData.setFromEmail(from.get("fromMail"));

            Map<String, String> receiveAddress = getReceiveAddress(msg, null);
            aiEmailData.setAddress(receiveAddress.get("address"));
            aiEmailData.setAddressEmail(receiveAddress.get("address_email"));

            // 检查是否包含附件（避免重复解析）
            boolean hasAttachment = isContainAttachment3(msg);
            aiEmailData.setIsEnclosure(hasAttachment ? "1" : "0");
            if (hasAttachment){
                // 解析邮件内容和附件（获取InputStream）
                Map<String, List<AiEmailAttachments>> parseResult = parseAttachments(msg);
                // 设置附件列表（包含InputStream）
                List<AiEmailAttachments> attachments = parseResult.get("attachments");
                aiEmailData.setMaterialList(attachments);
            }

            aiEmailData.setContent(getMailTextContent(msg));
            aiEmailData.setCreatorId(userId);
            aiEmailData.setType("1");

            return aiEmailData;
        } catch (Exception e) {
            log.error("解析邮件失败: {}", msg, e);
            return null;
        }
    }

    /**
     * 解析邮件内容和附件（返回InputStream）
     */
    private static Map<String, List<AiEmailAttachments>> parseAttachments(Part part)
            throws MessagingException, IOException {

        Map<String, List<AiEmailAttachments>> result = new HashMap<>();

        // 解析附件
        result.put("attachments", collectAttachmentsWithStreams(part));
        return result;
    }


    /**
     * 收集邮件附件（封装为InputStream）
     */
    private static List<AiEmailAttachments> collectAttachmentsWithStreams(Part part)
            throws MessagingException, IOException {

        List<AiEmailAttachments> attachments = new ArrayList<>();

        if (part.isMimeType("multipart/*")) {
            Multipart multipart = (Multipart) part.getContent();
            for (int i = 0; i < multipart.getCount(); i++) {
                BodyPart bodyPart = multipart.getBodyPart(i);
                String disposition = bodyPart.getDisposition();

                // 处理附件部分
                if (Part.ATTACHMENT.equalsIgnoreCase(disposition) ||
                        Part.INLINE.equalsIgnoreCase(disposition) ||
                        bodyPart.getFileName() != null) {

                    attachments.add(createAttachmentWithInputStream(bodyPart));
                }
                // 处理嵌套附件
                else if (bodyPart.isMimeType("multipart/*")) {
                    List<AiEmailAttachments> nestedAttachments =
                            collectAttachmentsWithStreams(bodyPart);
                    attachments.addAll(nestedAttachments);
                }
            }
        }
        return attachments;
    }

    /**
     * 创建附件对象（包含InputStream）
     */
    private static AiEmailAttachments createAttachmentWithInputStream(BodyPart bodyPart)
            throws MessagingException, IOException {

        AiEmailAttachments attachment = new AiEmailAttachments();

        // 获取文件名（处理编码）
        String fileName = getAttachmentFileName(bodyPart);
        attachment.setAttachmentName(fileName);


        // 封装InputStream为ByteArrayInputStream（避免流关闭问题）
        try {
            Object content = bodyPart.getContent();

            // 处理不同类型的附件内容
            if (content instanceof InputStream) {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                IOUtils.copy((InputStream) content, baos);
                attachment.setAttachmentUrl(OssUtil.getOssUrl(new ByteArrayInputStream(baos.toByteArray()), "material/email/" + fileName));
            }else if (content instanceof DataHandler) {
                DataHandler dh = (DataHandler) content;
                try (InputStream is = dh.getInputStream()) {
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    IOUtils.copy(is, baos);
                    attachment.setAttachmentUrl(OssUtil.getOssUrl(new ByteArrayInputStream(baos.toByteArray()), "material/email/" + fileName));
                }
            }else {
                log.warn("未知附件内容类型: {}", content.getClass().getName());
            }
        } catch (Exception e) {
            log.error("创建附件输入流失败: {}", fileName, e);
        }

        // 设置其他元数据
        attachment.setEmailType("1");
        attachment.setCreateTime(new Date());

        return attachment;
    }

    /**
     * 获取安全的附件文件名（解决编码问题）
     */
    private static String getAttachmentFileName(BodyPart bodyPart)
            throws MessagingException, UnsupportedEncodingException {

        String fileName = bodyPart.getFileName();
        if (fileName == null) {
            return "unnamed_attachment_" + System.currentTimeMillis();
        }

        // 处理 =?UTF-8?B?...?= 格式编码
        if (fileName.startsWith("=?") && fileName.contains("?B?") && fileName.endsWith("?=")) {
            return MimeUtility.decodeText(fileName);
        }
        return fileName;
    }


    /**
     * 解析发送时间（处理日期格式异常）
     */
    private static LocalDateTime parseSentDate(MimeMessage msg) {
        try {
            Date sentDate = msg.getSentDate();
            if (sentDate != null) {
                return sentDate.toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();
            }
        } catch (MessagingException e) {
            log.warn("解析邮件发送时间失败", e);
        }
        return LocalDateTime.now(); // 默认使用当前时间
    }

    /**
     * 高级邮件内容解析方法（最终优化版）
     * 支持：保留样式、过滤冗余换行符、处理图片和内嵌资源
     */
    public static String getMailTextContent(Part part) {
        try {
            ContentCollector collector = new ContentCollector();
            parsePartContent(part, collector, new HashSet<>());

            // 优先级: HTML > 纯文本
            if (collector.htmlContent.length() > 0) {
                String html = collector.htmlContent.toString();
                // 处理多余换行符和图片问题
                return filterRedundantNewlines(html);
            } else if (collector.plainContent.length() > 0) {
                return filterRedundantNewlines(collector.plainContent.toString());
            }
            return "（无正文内容）";
        } catch (Exception e) {
            log.error("解析邮件内容失败", e);
            return "无法解析邮件内容";
        }
    }

    /**
     * 递归解析邮件部件（增强版）
     */
    private static void parsePartContent(Part part, ContentCollector collector, Set<String> processedCids)
            throws MessagingException, IOException {

        String contentType = part.getContentType();
        log.debug("处理内容类型: {}", contentType);

        // 纯文本处理
        if (part.isMimeType("text/plain")) {
            String text = (String) part.getContent();
            if (text != null && !text.trim().isEmpty()) {
                collector.plainContent.append(text).append("\n");
            }
        }
        // HTML格式处理
        else if (part.isMimeType("text/html")) {
            String html = (String) part.getContent();
            if (html != null && !html.trim().isEmpty()) {
                // 修复 HTML 中的常见问题
                collector.htmlContent.append(fixHtmlContent(html));
            }
        }
        // 多部分内容（含嵌套）
        else if (part.isMimeType("multipart/*")) {
            Multipart multipart = (Multipart) part.getContent();
            for (int i = 0; i < multipart.getCount(); i++) {
                BodyPart bodyPart = multipart.getBodyPart(i);
                String disposition = bodyPart.getDisposition();

                // 处理附件和内联资源
                if (disposition != null &&
                        (disposition.equalsIgnoreCase(Part.ATTACHMENT) ||
                                disposition.equalsIgnoreCase(Part.INLINE))) {

                    // 提取 CID 引用
                    String cid = extractCid(bodyPart);

                    // 处理内联图片（CID引用）
                    if (disposition.equalsIgnoreCase(Part.INLINE) && cid != null) {
                        processedCids.add(cid.toLowerCase());
                    }
                    // 附件直接跳过
                    continue;
                }

                parsePartContent(bodyPart, collector, processedCids);
            }
        }
        // 处理内联资源（CID引用）
        else if (part.getDisposition() != null &&
                part.getDisposition().equalsIgnoreCase(Part.INLINE)) {
            String cid = extractCid(part);
            if (cid != null && !processedCids.contains(cid.toLowerCase())) {
                collector.htmlContent.append("[内联内容: ").append(part.getFileName()).append("]");
                processedCids.add(cid.toLowerCase());
            }
        }
        // 嵌套邮件处理
        else if (part.isMimeType("message/rfc822")) {
            parsePartContent((Part) part.getContent(), collector, processedCids);
        }
    }

    /**
     * 内容收集容器
     */
    private static class ContentCollector {
        public final StringBuilder plainContent = new StringBuilder();
        public final StringBuilder htmlContent = new StringBuilder();
    }

    /**
     * 修复 HTML 内容常见问题
     */
    private static String fixHtmlContent(String html) {
        if (html == null || html.isEmpty()) return "";

        // 1. 标准化换行符 - 将所有 \r\n 替换为 \n
        html = html.replace("\r\n", "\n");

        // 2. 修复常见的HTML结构问题
        html = html
                .replace("<head>\r", "<head>")
                .replace("</head>\r", "</head>")
                .replace("<body>\r", "<body>")
                .replace("</body>\r", "</body>")
                .replace("</html>\r", "</html>");

        // 3. 修复重复的body和html结束标签
        html = html.replace("</body></body>", "</body>");
        html = html.replace("</html></html>", "</html>");

        return html;
    }

    /**
     * 过滤多余换行符
     */
    private static String filterRedundantNewlines(String content) {
        if (content == null || content.isEmpty()) return "";

        // 1. 压缩连续换行符（最多保留2个）
        content = content.replaceAll("(\r?\n){3,}", "\n\n");

        // 2. 修复HTML标签内的换行符
        content = content
                .replace(">\n\n<", "><")
                .replace(">\n<", "><")
                .replace(">\r\n<", "><");

        // 3. 移除结束标签前的换行符
        content = content.replaceAll("\\s+</", "</");

        return content;
    }

    /**
     * 提取CID引用
     */
    private static String extractCid(Part part) throws MessagingException {
        String[] header = part.getHeader("Content-ID");
        if (header != null && header.length > 0) {
            String cid = header[0];
            if (cid.startsWith("<") && cid.endsWith(">")) {
                cid = cid.substring(1, cid.length() - 1);
            }
            return cid;
        }
        return null;
    }


    /**
     * 优化获取主题方法（处理编码问题）
     */
    public static String getSubject3(MimeMessage msg) {
        try {
            String subject = msg.getSubject();
            if (subject != null) {
                // 解码主题（处理 =?ISO-8859-1?Q? 等编码格式）
                subject = MimeUtility.decodeText(subject);
            }
            return subject == null ? "(无主题)" : subject;
        } catch (MessagingException | UnsupportedEncodingException e) {
            log.warn("解析邮件主题失败", e);
            return "(主题解析失败)";
        }
    }

    /**
     * 检测是否包含附件（增强版）
     */
    public static boolean isContainAttachment3(Part part)
            throws MessagingException, IOException {

        boolean result = false;
        if (part.isMimeType("multipart/*")) {
            Multipart multipart = (Multipart) part.getContent();
            for (int i = 0; i < multipart.getCount(); i++) {
                BodyPart bodyPart = multipart.getBodyPart(i);
                String disp = bodyPart.getDisposition();

                if (disp != null && (disp.equalsIgnoreCase(Part.ATTACHMENT) ||
                        disp.equalsIgnoreCase(Part.INLINE))) {
                    return true;
                }

                // 处理嵌套情况
                result = isContainAttachment(bodyPart) || result;
            }
        }
        return result;
    }
    


    /**
     * 获得邮件发件人
     *
     * @param msg 邮件内容
     * @return 姓名 <Email地址>
     * @throws MessagingException
     * @throws UnsupportedEncodingException
     */
    public static Map<String,String> getFrom(MimeMessage msg) throws MessagingException, UnsupportedEncodingException {
        Map<String,String> map = new HashMap<>();
        Address[] froms = msg.getFrom();
        if (froms.length < 1)
            throw new MessagingException("没有发件人!");

        InternetAddress address = (InternetAddress) froms[0];
        String person = address.getPersonal();
        if (person != null) {
            person = MimeUtility.decodeText(person) + " ";
        } else {
            person = "";
        }
        map.put("from",person.replace("\"", "").trim());
        map.put("fromMail",address.getAddress());
        return map;
    }


    public static Map<String,String> getReceiveAddress(MimeMessage msg, Message.RecipientType type) throws MessagingException {
        Map<String,String> map = new HashMap<>();
        Address[] addresses = (type == null) ? msg.getAllRecipients() : msg.getRecipients(type);

        if (addresses == null || addresses.length < 1) {
            throw new MessagingException("没有收件人!");
        }

        for (Address address : addresses) {
            if (address instanceof InternetAddress) {
                InternetAddress internetAddress = (InternetAddress) address;
                String name = internetAddress.getPersonal() != null ? internetAddress.getPersonal() : "";
                String email = internetAddress.getAddress();
                //去掉name两边的”|“和空格
                name = name.replace("\"", "").trim();
                map.put("address", name);
                map.put("address_email", email);
            }
        }
        return map;
    }


    /**
     * 判断邮件中是否包含附件
     *
     * @return 邮件中存在附件返回true，不存在返回false
     * @throws MessagingException
     * @throws IOException
     */
    public static boolean isContainAttachment(Part part) throws MessagingException, IOException {
        boolean flag = false;
        if (part.isMimeType("multipart/*")) {
            MimeMultipart multipart = (MimeMultipart) part.getContent();
            int partCount = multipart.getCount();
            for (int i = 0; i < partCount; i++) {
                BodyPart bodyPart = multipart.getBodyPart(i);
                String disp = bodyPart.getDisposition();
                if (disp != null && (disp.equalsIgnoreCase(Part.ATTACHMENT) || disp.equalsIgnoreCase(Part.INLINE))) {
                    flag = true;
                } else if (bodyPart.isMimeType("multipart/*")) {
                    flag = isContainAttachment(bodyPart);
                } else {
                    String contentType = bodyPart.getContentType();
                    if (contentType.contains("application")) {
                        flag = true;
                    }

                    if (contentType.contains("name")) {
                        flag = true;
                    }
                }

                if (flag) break;
            }
        } else if (part.isMimeType("message/rfc822")) {
            flag = isContainAttachment((Part) part.getContent());
        }
        return flag;
    }

    /**
     * 判断邮件是否已读
     *
     * @param msg 邮件内容
     * @return 如果邮件已读返回true, 否则返回false
     * @throws MessagingException
     */
    public static boolean isSeen(MimeMessage msg) throws MessagingException {
        return msg.getFlags().contains(Flags.Flag.SEEN);
    }

    /**
     * 判断邮件是否需要阅读回执
     *
     * @param msg 邮件内容
     * @return 需要回执返回true, 否则返回false
     * @throws MessagingException
     */
    public static boolean isReplySign(MimeMessage msg) throws MessagingException {
        boolean replySign = false;
        String[] headers = msg.getHeader("Disposition-Notification-To");
        if (headers != null)
            replySign = true;
        return replySign;
    }

    /**
     * 获得邮件的优先级
     *
     * @param msg 邮件内容
     * @return 1(High):紧急  2:普通(Normal)  3:低(Low)
     * @throws MessagingException
     */
    public static String getPriority(MimeMessage msg) throws MessagingException {
        String priority = "2";
        String[] headers = msg.getHeader("X-Priority");
        if (headers != null) {
            String headerPriority = headers[0];
            if (headerPriority.contains("1") || headerPriority.contains("High"))
                priority = "1";
            else if (headerPriority.contains("5") || headerPriority.contains("Low"))
                priority = "3";
            else
                priority = "2";
        }
        return priority;
    }


    /**
     * 提取邮箱域名部分
     */
    public static String getEmailType(String email) {
        int atIndex = email.lastIndexOf('@');
        if (atIndex >= 0 && atIndex < email.length() - 1) {
            return email.substring(atIndex + 1); // 关键修改：返回@符号之后的部分
        }
        return "";
    }


}
